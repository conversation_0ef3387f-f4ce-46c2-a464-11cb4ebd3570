package com.genco.service.service;

import com.genco.common.token.TikTokOauthToken;

/**
 * TikTok Token刷新服务接口
 * 统一处理TikTok token的刷新逻辑
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-08
 */
public interface TikTokTokenRefreshService {
    
    /**
     * 刷新TikTok access token
     * 
     * @param appKey TikTok应用Key
     * @param appSecret TikTok应用Secret
     * @param refreshToken 刷新令牌
     * @return 刷新后的token信息
     * @throws Exception 刷新失败时抛出异常
     */
    TikTokOauthToken refreshAccessToken(String appKey, String appSecret, String refreshToken) throws Exception;
    
    /**
     * 刷新TikTok access token并更新到系统配置
     * 
     * @return 是否刷新成功
     */
    boolean refreshAndUpdateToken();
}
