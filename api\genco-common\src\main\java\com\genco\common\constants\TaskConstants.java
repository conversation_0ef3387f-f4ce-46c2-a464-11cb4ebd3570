package com.genco.common.constants;

/**
 * 定时任务常量类
 */
public class TaskConstants {

    /**
     * 订单支付成功后Task
     */
    public static final String ORDER_TASK_PAY_SUCCESS_AFTER = "orderPaySuccessTask";
    
    /**
     * 订单收货后Task
     */
    public static final String ORDER_TASK_REDIS_KEY_AFTER_TAKE_BY_USER = "alterOrderTakeByUser";
    
    /**
     * 充值支付成功后Task
     */
    public static final String RECHARGE_TASK_PAY_SUCCESS_AFTER = "rechargePaySuccessTask";

    /**
     * 任务类型 - 邀请首单
     */
    public static final String TASK_TYPE_INVITE_FIRST_ORDER = "invite_first_order";

    /**
     * 任务状态 - 有效
     */
    public static final Integer TASK_STATUS_ACTIVE = 1;

    /**
     * 任务状态 - 无效
     */
    public static final Integer TASK_STATUS_INACTIVE = 0;
}
