package com.genco.service.service.impl;

import cn.hutool.core.util.StrUtil;
import com.genco.common.constants.SysConfigConstants;
import com.genco.common.model.affiliate.AffiliateProductHistoryV2;
import com.genco.common.model.product.StoreProduct;
import com.genco.common.request.AffiliateProductSearchRequestV2;
import com.genco.common.response.AffiliateProductResponseV2;
import com.genco.common.response.ProductShareLinkResponse;
import com.genco.service.service.AffiliateProductHistoryV2Service;
import com.genco.service.service.AffiliateProductServiceV2;
import com.genco.service.service.StoreProductService;
import com.genco.service.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tiktokshop.open.sdk_java.api.AffiliateCreatorV202501Api;
import tiktokshop.open.sdk_java.invoke.ApiClient;
import tiktokshop.open.sdk_java.invoke.ApiException;
import tiktokshop.open.sdk_java.invoke.ApiResponse;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202501.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 联盟选品服务实现类 V2
 */
@Slf4j
@Service
public class AffiliateProductServiceV2Impl implements AffiliateProductServiceV2 {

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private AffiliateProductHistoryV2Service affiliateProductHistoryV2Service;

    @Autowired
    private StoreProductService storeProductService;

    @Override
    public AffiliateProductResponseV2 searchProducts(AffiliateProductSearchRequestV2 request) throws ApiException {
        log.info("开始联盟选品查询 V2，请求参数: {}", request);

        long startTime = System.currentTimeMillis();

        // 创建API客户端
        ApiClient apiClient = createApiClient();
        AffiliateCreatorV202501Api apiInstance = new AffiliateCreatorV202501Api(apiClient);

        // 构建请求体
        CreatorSelectAffiliateProductRequestBody requestBody = buildRequestBody(request);

        // 调用TikTok API
        log.info("调用TikTok联盟选品API V2，pageSize: {}, sortType: {}",
                request.getPageSize(), request.getSortType());

        ApiResponse<CreatorSelectAffiliateProductResponse> responseInfo = apiInstance
                .affiliateCreator202501SelectionProductsSearchPostWithHttpInfo(
                        apiClient.getTokens(),
                        "application/json",
                        request.getPageToken(),
                        request.getPageSize().intValue(),
                        requestBody
                );

        CreatorSelectAffiliateProductResponse response = responseInfo.getData();

        log.info("TikTok API V2调用成功，响应码: {}, 消息: {}", response.getCode(), response.getMessage());

        // 计算搜索耗时
        long searchDuration = System.currentTimeMillis() - startTime;

        // 转换响应数据
        return convertToResponse(response, searchDuration);
    }

    /**
     * 创建API客户端
     */
    private ApiClient createApiClient() {
        ApiClient apiClient = new ApiClient();

        String appKey = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_APP_KEY);
        String appSecret = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_APP_SECRET);
        String accessToken = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_ACCESS_TOKEN);

        if (StrUtil.isBlank(appKey) || StrUtil.isBlank(appSecret) || StrUtil.isBlank(accessToken)) {
            throw new RuntimeException("TikTok API配置不完整，请检查应用密钥、应用秘钥和访问令牌配置");
        }

        apiClient.setAppkey(appKey);
        apiClient.setSecret(appSecret);
        apiClient.setTokens(accessToken);

        return apiClient;
    }

    /**
     * 构建请求体
     */
    private CreatorSelectAffiliateProductRequestBody buildRequestBody(AffiliateProductSearchRequestV2 request) {
        CreatorSelectAffiliateProductRequestBody requestBody =
                new CreatorSelectAffiliateProductRequestBody();

        // 构建过滤参数
        CreatorSelectAffiliateProductRequestBodyFilterParams filterParams =
                new CreatorSelectAffiliateProductRequestBodyFilterParams();

        // 设置商品ID过滤（用于精确查询）
        if (request.getProductIds() != null && !request.getProductIds().isEmpty()) {
            filterParams.setProductIds(request.getProductIds());
        }

        // 设置标题关键词
        if (StrUtil.isNotBlank(request.getTitleKeyword())) {
             filterParams.setTitleKeyword(request.getTitleKeyword());
        }

        // 设置分类ID过滤
        if (request.getCategoryIds() != null && !request.getCategoryIds().isEmpty()) {
            filterParams.setCategoryIds(request.getCategoryIds());
        }

        // 设置佣金率范围
        CreatorSelectAffiliateProductRequestBodyFilterParamsCommissionRateRange commissionRateRange = new CreatorSelectAffiliateProductRequestBodyFilterParamsCommissionRateRange();
        if (request.getCommissionRateGe() != null) {
            commissionRateRange.setRateGe(request.getCommissionRateGe());
        }
        if (request.getCommissionRateLe() != null) {
            commissionRateRange.setRateLe(request.getCommissionRateLe());
        }

        filterParams.setCommissionRateRange(commissionRateRange);

        // 设置价格范围
        CreatorSelectAffiliateProductRequestBodyFilterParamsPriceRange priceRange = new CreatorSelectAffiliateProductRequestBodyFilterParamsPriceRange();
        if (request.getPriceRangeGe() != null) {
            priceRange.setPriceGe(request.getPriceRangeGe());
        }
        if (request.getPriceRangeLe() != null) {
            priceRange.setPriceLe(request.getPriceRangeLe());
        }
        filterParams.setPriceRange(priceRange);

        // 设置店铺评分范围
        CreatorSelectAffiliateProductRequestBodyFilterParamsShopRatingRange shopRatingRange = new CreatorSelectAffiliateProductRequestBodyFilterParamsShopRatingRange();
        if (request.getShopRatingGe() != null) {
            shopRatingRange.setRatingGe(request.getShopRatingGe());
        }
        if (request.getShopRatingLe() != null) {
            shopRatingRange.setRatingLe(request.getShopRatingLe());
        }
        filterParams.setShopRatingRange(shopRatingRange);

        // 设置销量范围
        CreatorSelectAffiliateProductRequestBodyFilterParamsSoldQuantityRange soldQuantityRange = new CreatorSelectAffiliateProductRequestBodyFilterParamsSoldQuantityRange();
        if (request.getSoldQuantityGe() != null) {
            soldQuantityRange.setQuantityGe(request.getSoldQuantityGe());
        }
        if (request.getSoldQuantityLe() != null) {
            soldQuantityRange.setQuantityLe(request.getSoldQuantityLe());
        }
        filterParams.setSoldQuantityRange(soldQuantityRange);


        // 设置产品池ID
        if (request.getPoolIds() != null && !request.getPoolIds().isEmpty()) {
            filterParams.setPoolIds(request.getPoolIds());
        }

        requestBody.setFilterParams(filterParams);

        // 设置排序类型
        if (StrUtil.isNotBlank(request.getSortType())) {
            CreatorSelectAffiliateProductRequestBodySortParams sortParams = new CreatorSelectAffiliateProductRequestBodySortParams();
            sortParams.setSortType(request.getSortType());
            requestBody.setSortParams(sortParams);
        }

        return requestBody;
    }

    /**
     * 转换响应数据
     */
    private AffiliateProductResponseV2 convertToResponse(CreatorSelectAffiliateProductResponse response, long searchDuration) {
        AffiliateProductResponseV2 result = new AffiliateProductResponseV2();
        result.setSearchDuration(searchDuration);

        if (response.getData() != null) {
            CreatorSelectAffiliateProductResponseData data = response.getData();

            result.setNextPageToken(data.getNextPageToken());
            // 安全转换Integer到Long
            if (data.getTotalCount() != null) {
                result.setTotalCount(data.getTotalCount().longValue());
            }

            // 转换产品列表
            if (data.getProducts() != null) {
                Map<String, ProductShareLinkResponse> shareLinkResponseMap = genShareLink(data.getProducts());
                List<AffiliateProductResponseV2.AffiliateProductV2> products = data.getProducts().stream()
                        .map(p -> this.convertProduct(p, shareLinkResponseMap))
                        .collect(Collectors.toList());

                // 同步TikTok商品数据到历史记录表V2
                syncTikTokProductsToHistoryV2(data.getProducts(), shareLinkResponseMap);

                // 批量查询已入库的商品，避免for循环查询数据库
                List<String> productIds = products.stream()
                        .map(AffiliateProductResponseV2.AffiliateProductV2::getId)
                        .collect(Collectors.toList());
                List<StoreProduct> existingProducts = storeProductService.getByOutProductIds(productIds);
                List<String> importedProductIds = existingProducts.stream()
                        .map(StoreProduct::getOutProductId)
                        .collect(Collectors.toList());

                // 设置isImported字段
                products.forEach(product -> {
                    product.setIsImported(importedProductIds.contains(product.getId()));
                });

                result.setProducts(products);
            } else {
                result.setProducts(new ArrayList<>());
            }
        }

        return result;
    }

    private Map<String, ProductShareLinkResponse> genShareLink(List<CreatorSelectAffiliateProductResponseDataProducts> list) {
        Map<String, ProductShareLinkResponse> result = new HashMap<>();
        if (list == null || list.isEmpty()) {
            return result;
        }

        // 提取所有商品ID
        List<String> productIds = list.stream()
                .map(CreatorSelectAffiliateProductResponseDataProducts::getId)
                .collect(Collectors.toList());

        // 批量查询history表中已有的转链数据
        List<AffiliateProductHistoryV2> existingHistories = affiliateProductHistoryV2Service.getByProductIds(productIds);
        Map<String, AffiliateProductHistoryV2> historyMap = existingHistories.stream()
                .filter(h -> StrUtil.isNotBlank(h.getAffiliateShareLink())) // 只保留有转链的记录
                .collect(Collectors.toMap(AffiliateProductHistoryV2::getProductId, h -> h));

        log.info("从history表获取到{}个商品的转链数据", historyMap.size());

        // 分离需要生成转链的商品和已有转链的商品
        List<CreatorSelectAffiliateProductResponseDataProducts> needGenerateProducts = new ArrayList<>();

        for (CreatorSelectAffiliateProductResponseDataProducts product : list) {
            String productId = product.getId();
            AffiliateProductHistoryV2 history = historyMap.get(productId);

            if (history != null && StrUtil.isNotBlank(history.getAffiliateShareLink())) {
                // 从history表获取转链数据
                ProductShareLinkResponse linkResp = new ProductShareLinkResponse();
                linkResp.setShareLink(history.getAffiliateShareLink());
                linkResp.setTags(history.getShareLinkTags());
                result.put(productId, linkResp);
                log.debug("从history表获取商品{}的转链: {}", productId, history.getAffiliateShareLink());
            } else {
                // 需要生成转链的商品
                needGenerateProducts.add(product);
            }
        }

        // 为需要生成转链的商品调用API
        if (!needGenerateProducts.isEmpty()) {
            log.info("需要为{}个商品生成新的转链", needGenerateProducts.size());

            for (CreatorSelectAffiliateProductResponseDataProducts product : needGenerateProducts) {
                ProductShareLinkResponse linkResp = storeProductService.getProductShareLinkBy(
                        product.getMainImageUrl(), product.getId(), "genco");
                result.put(product.getId(), linkResp);
                // 立即更新history表中的转链数据
                updateHistoryWithShareLink(product.getId(), linkResp);
            }
        }

        log.info("转链处理完成，从缓存获取: {}个，新生成: {}个", historyMap.size(), needGenerateProducts.size());
        return result;
    }

    /**
     * 转换单个产品数据
     */
    private AffiliateProductResponseV2.AffiliateProductV2 convertProduct(
            CreatorSelectAffiliateProductResponseDataProducts product, Map<String, ProductShareLinkResponse> linkResponseMap) {

        AffiliateProductResponseV2.AffiliateProductV2 result = new AffiliateProductResponseV2.AffiliateProductV2();

        // 基本信息
        result.setId(product.getId());
        result.setTitle(product.getTitle());
        result.setMainImageUrl(product.getMainImageUrl());
        result.setBrandName(product.getBrandName());
        if (linkResponseMap != null && linkResponseMap.get(product.getId()) != null) {
            result.setAffiliateShareLink(linkResponseMap.get(product.getId()).getShareLink());
            result.setShareLinkTags(linkResponseMap.get(product.getId()).getTags());
        }

        // 转换价格信息
        if (product.getPrice() != null) {
            result.setPrice(convertPriceInfoFromPrice(product.getPrice()));
        }

        // 转换佣金信息
        result.setCommission(convertCommissionInfo(product));

        // 转换店铺信息
        if (product.getShop() != null) {
            result.setShop(convertShopInfo(product.getShop()));
        }

        // 评价信息 - 使用默认值
        if (product.getReview() != null) {
            AffiliateProductResponseV2.ReviewV2 defaultReview = new AffiliateProductResponseV2.ReviewV2();
            defaultReview.setCount(product.getReview().getCount());
            defaultReview.setOverallScore(product.getReview().getOverallScore());
            result.setReview(defaultReview);
        }

        // 库存信息 - 使用默认值
        if (product.getStock() != null) {
            AffiliateProductResponseV2.StockInfoV2 defaultStock = new AffiliateProductResponseV2.StockInfoV2();
            defaultStock.setQuantity(product.getStock().getQuantity());
            result.setStock(defaultStock);
        }

        // 市场表现信息 - 使用默认值
        if (product.getMarketPerformance() != null) {
            AffiliateProductResponseV2.MarketPerformanceV2 defaultMarketPerformance = new AffiliateProductResponseV2.MarketPerformanceV2();
            defaultMarketPerformance.setHistoricalSoldQuantity(product.getMarketPerformance().getHistoricalSoldQuantity());
            result.setMarketPerformance(defaultMarketPerformance);
        }

        // 初始化isImported字段，将在convertToResponse方法中批量设置
        result.setIsImported(false);

        return result;
    }

    /**
     * 转换价格信息（从Price对象）
     */
    private AffiliateProductResponseV2.PriceInfoV2 convertPriceInfoFromPrice(
            CreatorSelectAffiliateProductResponseDataProductsPrice price) {
        AffiliateProductResponseV2.PriceInfoV2 priceInfo = new AffiliateProductResponseV2.PriceInfoV2();

        if (price.getFloorPrice() != null) {
            priceInfo.setFloorPrice(price.getFloorPrice());
        }
        if (price.getCeilingPrice() != null) {
            priceInfo.setCeilingPrice(price.getCeilingPrice());
        }
        priceInfo.setCurrencyName(price.getCurrency());

        return priceInfo;
    }

    /**
     * 转换佣金信息
     */
    private AffiliateProductResponseV2.CommissionInfoV2 convertCommissionInfo(
            CreatorSelectAffiliateProductResponseDataProducts product) {
        AffiliateProductResponseV2.CommissionInfoV2 commissionInfo = new AffiliateProductResponseV2.CommissionInfoV2();
        if (product == null || product.getCommission() == null || product.getPrice() == null) {
            return commissionInfo;
        }
        if (product.getCommission().getRate() == null || product.getPrice().getFloorPrice() == null) {
            return commissionInfo;
        }
        // 安全获取佣金信息
        commissionInfo.setRate(product.getCommission().getRate());
        int rate = product.getCommission().getRate() / 100; // 百分比
        commissionInfo.setRateStr(rate + "%");
        // 计算佣金金额：floorPrice * rate / 100 / 100
        if (product.getPrice() != null) {
            String floorPriceStr = product.getPrice().getFloorPrice();
            if (floorPriceStr != null) {
                try {
                    BigDecimal floorPrice = new BigDecimal(floorPriceStr);
                    BigDecimal amount = floorPrice.multiply(new BigDecimal(Integer.toString(rate)).divide(new BigDecimal(100), 2, RoundingMode.DOWN));
                    commissionInfo.setAmount(Long.toString(amount.longValue()));
                } catch (NumberFormatException e) {
                    // 如果价格格式不正确，设置为null
                    commissionInfo.setAmount(null);
                }
            }
        }
        return commissionInfo;
    }

    /**
     * 转换店铺信息
     */
    private AffiliateProductResponseV2.ShopInfoV2 convertShopInfo(
            CreatorSelectAffiliateProductResponseDataProductsShop shop) {
        AffiliateProductResponseV2.ShopInfoV2 shopInfo = new AffiliateProductResponseV2.ShopInfoV2();
        shopInfo.setName(shop.getName());
        return shopInfo;
    }

    /**
     * 同步TikTok商品数据到历史记录表V2
     */
    private void syncTikTokProductsToHistoryV2(List<CreatorSelectAffiliateProductResponseDataProducts> products, Map<String, ProductShareLinkResponse> shareLinkResponseMap) {
        if (products == null || products.isEmpty()) {
            return;
        }

        try {
             affiliateProductHistoryV2Service.syncTikTokProducts(products, shareLinkResponseMap);
            log.info("同步{}个商品到历史记录表V2", products.size());
        } catch (Exception e) {
            log.error("同步商品到历史记录表V2失败", e);
        }
    }

    /**
     * 更新history表中的转链数据
     */
    private void updateHistoryWithShareLink(String productId, ProductShareLinkResponse linkResp) {
        if (linkResp == null || StrUtil.isBlank(linkResp.getShareLink())) {
            return;
        }
        AffiliateProductHistoryV2 history = affiliateProductHistoryV2Service.getByProductId(productId);
        if (history != null) {
            history.setAffiliateShareLink(linkResp.getShareLink());
            history.setShareLinkTags(linkResp.getTags());
            history.setUpdateTime(new Date());
            affiliateProductHistoryV2Service.updateById(history);
            log.debug("更新商品{}的转链数据到history表", productId);
        }
    }
}
