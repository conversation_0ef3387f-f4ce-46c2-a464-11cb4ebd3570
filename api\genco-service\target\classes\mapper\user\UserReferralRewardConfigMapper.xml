<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.genco.service.dao.UserReferralRewardConfigDao">

    <!-- 根据用户ID查询拉新奖励配置 -->
    <select id="selectByUid" resultType="com.genco.common.model.user.UserReferralRewardConfig" parameterType="java.lang.Integer">
        SELECT
            id,
            uid,
            referral_count as referralCount,
            first_order_count as firstOrderCount,
            reward_amount as rewardAmount,
            status,
            redeemed_referral_count as redeemedReferralCount,
            redeemed_first_order_count as redeemedFirstOrderCount,
            create_time as createTime,
            update_time as updateTime
        FROM eb_user_referral_reward_config
        WHERE uid = #{uid} AND status = 1
    </select>

    <!-- 查询用户的有效拉新奖励配置（优先级：银级会员配置 > 平台配置） -->
    <select id="selectEffectiveConfigByUid" resultType="com.genco.common.model.user.UserReferralRewardConfig" parameterType="java.lang.Integer">
        SELECT
            config.id,
            config.uid,
            config.referral_count as referralCount,
            config.first_order_count as firstOrderCount,
            config.reward_amount as rewardAmount,
            config.status,
            config.redeemed_referral_count as redeemedReferralCount,
            config.redeemed_first_order_count as redeemedFirstOrderCount,
            config.create_time as createTime,
            config.update_time as updateTime
        FROM eb_user u
        LEFT JOIN eb_user parent_user ON u.spread_uid = parent_user.uid
        LEFT JOIN eb_system_user_level parent_level ON parent_user.level = parent_level.id AND parent_level.grade >= 1
        LEFT JOIN eb_user_referral_reward_config config ON parent_user.uid = config.uid AND config.status = 1
        WHERE u.uid = #{uid}
        AND config.id IS NOT NULL
        LIMIT 1
    </select>

    <!-- 获取指定推广人的下级用户首单总数 -->
    <select id="getTotalFirstOrderCountBySpreadUid" resultType="java.lang.Integer" parameterType="java.lang.Integer">
        SELECT COUNT(DISTINCT o.uid) as firstOrderCount
        FROM eb_store_order o
        INNER JOIN eb_user u ON o.uid = u.uid
        WHERE u.spread_uid = #{spreadUid}
        AND o.paid = 1
        AND o.is_del = 0
        AND o.refund_status = 0
        AND o.id = (
            SELECT MIN(o2.id)
            FROM eb_store_order o2
            WHERE o2.uid = o.uid
            AND o2.paid = 1
            AND o2.is_del = 0
            AND o2.refund_status = 0
        )
    </select>

</mapper>
