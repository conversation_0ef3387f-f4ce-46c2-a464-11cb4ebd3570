<template>
  <div class="divBox relative">
    <el-card class="box-card">
      <div class="container mt-1">
        <el-form v-model="searchForm" inline size="small">
          <el-form-item :label="$t('user.levelUpgrade.orderNo') + '：'">
            <el-input
              v-model="searchForm.orderNo"
              :placeholder="$t('user.levelUpgrade.enterOrderNo')"
              clearable
            />
          </el-form-item>
          <el-form-item :label="$t('user.levelUpgrade.orderStatus') + '：'">
            <el-select
              v-model="searchForm.orderStatus"
              :placeholder="$t('user.levelUpgrade.selectStatus')"
              clearable
            >
              <el-option :label="$t('user.levelUpgrade.pending')" :value="0"></el-option>
              <el-option :label="$t('user.levelUpgrade.paid')" :value="1"></el-option>
              <el-option :label="$t('user.levelUpgrade.cancelled')" :value="2"></el-option>
              <el-option :label="$t('user.levelUpgrade.refunded')" :value="3"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <el-button size="small" type="primary" class="mr10" @click="getList">
        {{ $t("user.center.query") }}
      </el-button>

      <el-button size="small" type="" class="mr10" @click="resetSearch">
        {{ $t("user.center.reset") }}
      </el-button>
    </el-card>

    <el-card class="box-card" style="margin-top: 12px">
      <!-- 订单列表 -->
      <el-table
        v-loading="listLoading"
        :data="tableData"
        style="width: 100%"
        size="mini"
      >
        <el-table-column
          prop="orderNo"
          :label="$t('user.levelUpgrade.orderNo')"
          min-width="150"
        />
        <el-table-column
          prop="uid"
          :label="$t('user.levelUpgrade.userId')"
          min-width="80"
        />
        <el-table-column
          :label="$t('user.levelUpgrade.upgradeInfo')"
          min-width="150"
        >
          <template slot-scope="scope">
            <div>{{ getLevelName(scope.row.fromLevelId) }} → {{ getLevelName(scope.row.toLevelId) }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="upgradePrice"
          :label="$t('user.levelUpgrade.upgradeFee')"
          min-width="100"
        >
          <template slot-scope="scope">
            <span>Rp {{ scope.row.upgradePrice }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="paymentMethod"
          :label="$t('user.levelUpgrade.paymentMethod')"
          min-width="100"
        >
          <template slot-scope="scope">
            <span>{{ getPaymentMethodName(scope.row.paymentMethod) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="orderStatus"
          :label="$t('user.levelUpgrade.orderStatus')"
          min-width="100"
        >
          <template slot-scope="scope">
            <el-tag :type="getStatusColor(scope.row.orderStatus)">
              {{ getStatusName(scope.row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          :label="$t('user.levelUpgrade.createTime')"
          min-width="150"
        />
        <el-table-column
          prop="payTime"
          :label="$t('user.levelUpgrade.payTime')"
          min-width="150"
        />
        <el-table-column :label="$t('user.levelUpgrade.operation')" min-width="120" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.orderStatus === 0"
              type="text"
              size="small"
              @click="cancelOrder(scope.row.orderNo)"
              class="mr10"
            >
              {{ $t('user.levelUpgrade.cancelOrder') }}
            </el-button>
            <el-button type="text" size="small" @click="viewDetail(scope.row)">{{ $t('user.levelUpgrade.viewDetail') }}</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        class="mt20"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="searchForm.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchForm.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </el-card>

    <!-- 订单详情弹窗 -->
    <el-dialog :title="$t('user.levelUpgrade.orderDetail')" :visible.sync="detailVisible" width="600px">
      <div v-if="currentOrder">
        <el-descriptions :column="2" border>
          <el-descriptions-item :label="$t('user.levelUpgrade.orderNo')">{{ currentOrder.orderNo }}</el-descriptions-item>
          <el-descriptions-item :label="$t('user.levelUpgrade.userId')">{{ currentOrder.uid }}</el-descriptions-item>
          <el-descriptions-item :label="$t('user.levelUpgrade.fromLevel')">{{ getLevelName(currentOrder.fromLevelId) }}</el-descriptions-item>
          <el-descriptions-item :label="$t('user.levelUpgrade.toLevel')">{{ getLevelName(currentOrder.toLevelId) }}</el-descriptions-item>
          <el-descriptions-item :label="$t('user.levelUpgrade.upgradeFee')">Rp {{ currentOrder.upgradePrice }}</el-descriptions-item>
          <el-descriptions-item :label="$t('user.levelUpgrade.paymentMethod')">{{ getPaymentMethodName(currentOrder.paymentMethod) }}</el-descriptions-item>
          <el-descriptions-item :label="$t('user.levelUpgrade.orderStatus')">
            <el-tag :type="getStatusColor(currentOrder.orderStatus)">
              {{ getStatusName(currentOrder.orderStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('user.levelUpgrade.createTime')">{{ currentOrder.createTime }}</el-descriptions-item>
          <el-descriptions-item :label="$t('user.levelUpgrade.payTime')">{{ currentOrder.payTime || $t('user.levelUpgrade.unpaid') }}</el-descriptions-item>
          <el-descriptions-item :label="$t('user.levelUpgrade.remark')">{{ currentOrder.remark || $t('user.levelUpgrade.noRemark') }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { levelUpgradeOrderListApi, cancelLevelUpgradeOrderApi } from '@/api/user'

export default {
  name: 'UserLevelUpgrade',
  data() {
    return {
      searchForm: {
        orderNo: '',
        orderStatus: '',
        page: 1,
        limit: 20
      },
      tableData: [],
      total: 0,
      listLoading: false,
      detailVisible: false,
      currentOrder: null
    }
  },
  mounted() {
    // 延迟执行，确保组件完全挂载
    this.$nextTick(() => {
      this.getList()
    })
  },
  methods: {
    // 获取订单列表
    getList() {
      this.listLoading = true

      const params = {
        page: this.searchForm.page,
        limit: this.searchForm.limit
      }

      // 添加筛选条件
      if (this.searchForm.orderNo) {
        params.orderNo = this.searchForm.orderNo
      }
      if (this.searchForm.orderStatus !== '') {
        params.orderStatus = this.searchForm.orderStatus
      }

      levelUpgradeOrderListApi(params).then(res => {
        // 确保数据结构正确
        if (res && res.data) {
          this.tableData = res.data.list || []
          this.total = res.data.total || 0
        } else {
          this.tableData = []
          this.total = 0
        }
        this.listLoading = false
      }).catch(error => {
        console.error('获取订单列表失败:', error)
        this.tableData = []
        this.total = 0
        this.listLoading = false
        this.$message.error(this.$t('user.levelUpgrade.getListFailed'))
      })
    },
    
    // 重置搜索
    resetSearch() {
      this.searchForm = {
        orderNo: '',
        orderStatus: '',
        page: 1,
        limit: 20
      }
      this.getList()
    },
    
    // 分页大小改变
    handleSizeChange(val) {
      this.searchForm.limit = val
      this.searchForm.page = 1
      this.getList()
    },
    
    // 当前页改变
    handleCurrentChange(val) {
      this.searchForm.page = val
      this.getList()
    },
    
    // 取消订单
    cancelOrder(orderNo) {
      this.$confirm(this.$t('user.levelUpgrade.confirmCancel'), this.$t('common.confirm'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        cancelLevelUpgradeOrderApi(orderNo).then(() => {
          this.$message.success(this.$t('user.levelUpgrade.cancelSuccess'))
          this.getList()
        }).catch(error => {
          console.error('取消订单失败:', error)
          this.$message.error(this.$t('user.levelUpgrade.cancelFailed'))
        })
      }).catch(() => {
        // 用户取消操作
      })
    },
    
    // 查看详情
    viewDetail(order) {
      this.currentOrder = order
      this.detailVisible = true
    },
    
    // 获取等级名称
    getLevelName(levelId) {
      return this.$t(`user.levelUpgrade.levelNames.${levelId}`) || this.$t('user.levelUpgrade.unknownLevel')
    },

    // 获取支付方式名称
    getPaymentMethodName(method) {
      const methodMap = {
        'balance': this.$t('user.levelUpgrade.balancePayment')
      }
      return methodMap[method] || method
    },

    // 获取状态名称
    getStatusName(status) {
      const statusMap = {
        0: this.$t('user.levelUpgrade.pending'),
        1: this.$t('user.levelUpgrade.paid'),
        2: this.$t('user.levelUpgrade.cancelled'),
        3: this.$t('user.levelUpgrade.refunded')
      }
      return statusMap[status] || this.$t('user.levelUpgrade.unknownStatus')
    },
    
    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        0: 'warning',
        1: 'success',
        2: 'info', 
        3: 'danger'
      }
      return colorMap[status] || 'info'
    }
  }
}
</script>

<style scoped>
.demo-form-inline .el-form-item {
  margin-bottom: 10px;
}
.block {
  margin-top: 20px;
  text-align: right;
}
</style>
