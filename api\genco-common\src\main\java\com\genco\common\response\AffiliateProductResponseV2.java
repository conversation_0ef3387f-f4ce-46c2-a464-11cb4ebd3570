package com.genco.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 联盟选品响应对象 V2
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AffiliateProductResponseV2", description = "联盟选品响应对象 V2")
public class AffiliateProductResponseV2 implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "产品列表")
    private List<AffiliateProductV2> products;

    @ApiModelProperty(value = "下一页游标")
    private String nextPageToken;

    @ApiModelProperty(value = "总数量")
    private Long totalCount;

    // V2版本新增字段
    @ApiModelProperty(value = "搜索耗时（毫秒）")
    private Long searchDuration;

    @ApiModelProperty(value = "API版本")
    private String apiVersion = "v2";

    /**
     * 联盟产品信息 V2
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @ApiModel(value = "AffiliateProductV2", description = "联盟产品信息 V2")
    public static class AffiliateProductV2 implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "The product's unique id")
        private String id;

        @ApiModelProperty(value = "The product's name")
        private String title;

        @ApiModelProperty(value = "The product's cover image url")
        private String mainImageUrl;

        @ApiModelProperty(value = "The product's brand name")
        private String brandName;

        @ApiModelProperty(value = "The price info of the product")
        private PriceInfoV2 price;

        @ApiModelProperty(value = "The commission info, including the amount and rate of the product")
        private CommissionInfoV2 commission;

        @ApiModelProperty(value = "The review of the product")
        private ReviewV2 review;

        @ApiModelProperty(value = "The profile of shop to which the product belongs")
        private ShopInfoV2 shop;

        @ApiModelProperty(value = "The stock info of the product")
        private StockInfoV2 stock;

        @ApiModelProperty(value = "The market performance summary of the product")
        private MarketPerformanceV2 marketPerformance;

        @ApiModelProperty(value = "是否已入库")
        private Boolean isImported;

        @ApiModelProperty(value = "联盟分享链接")
        private String affiliateShareLink;

        @ApiModelProperty(value = "分享链接标签")
        private String shareLinkTags;

    }

    /**
     * 价格信息 V2
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @ApiModel(value = "PriceInfoV2", description = "价格信息 V2")
    public static class PriceInfoV2 implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "The minimum price of the product over all skus")
        private String floorPrice;

        @ApiModelProperty(value = "The maximum price of the product over all skus")
        private String ceilingPrice;

        @ApiModelProperty(value = "The three-letter code of the price currency, obeying the rules in ISO 4217")
        private String currencyName;
    }

    /**
     * 佣金信息 V2
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @ApiModel(value = "CommissionInfoV2", description = "佣金信息 V2")
    public static class CommissionInfoV2 implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "The commission rate of the product multiplied by 10000, eg. 1500 means 15%")
        private Integer rate;

        @ApiModelProperty(value = "The commission rate of the product multiplied, eg. rate / 10000")
        private String rateStr;

        @ApiModelProperty(value = "The commission amount of the product, which is equal to floor_price * commission_rate")
        private String amount;

    }

    /**
     * The review of the product V2
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @ApiModel(value = "ReviewV2", description = "The review of the product V2")
    public static class ReviewV2 implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "The count of reviews")
        private Integer count;

        @ApiModelProperty(value = "The average score of the product, the range is (0,5]")
        private String overallScore;

    }

    /**
     * 店铺信息 V2
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @ApiModel(value = "ShopInfoV2", description = "店铺信息 V2")
    public static class ShopInfoV2 implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "The name of the shop")
        private String name;

        @ApiModelProperty(value = "The rating of the shop, the range is (0,5]")
        private Double rating;

        @ApiModelProperty(value = "The logo URL of the shop")
        private String logoUrl;
    }

    /**
     * The stock info of the product V2
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @ApiModel(value = "StockInfoV2", description = "The stock info of the product V2")
    public static class StockInfoV2 implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "The detailed stock quantity of the product")
        private Integer quantity;
    }

    /**
     * The market performance summary of the product V2
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @ApiModel(value = "MarketPerformanceV2", description = "The market performance summary of the product V2")
    public static class MarketPerformanceV2 implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "The total number of products sold in history")
        private Integer historicalSoldQuantity;
    }
}
