<template>
  <div class="divBox">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
      </div>
      <el-table
        v-loading="listLoading"
        :data="tableData.data"
        style="width: 100%"
        size="mini"
      >
        <el-table-column
          prop="id"
          label="ID"
          min-width="50"
        />
        <el-table-column :label="$t('user.grade.levelIcon')" min-width="80">
          <template slot-scope="scope">
            <div class="demo-image__preview">
              <el-image
                style="width: 36px; height: 36px"
                :src="scope.row.icon"
                :preview-src-list="[scope.row.icon]"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          :label="$t('user.grade.levelName')"
          min-width="100"
        />
        <el-table-column
          prop="experience"
          :label="$t('user.grade.experience')"
          min-width="100"
        />
        <el-table-column
          prop="discount"
          :label="$t('user.grade.discount') + '(%)'"
          min-width="100"
        />
        <el-table-column
          prop="commissionRate"
          :label="$t('user.grade.commissionRate') + '(%)'"
          min-width="120"
        />
        <el-table-column
          prop="upgradeType"
          :label="$t('user.grade.upgradeType')"
          min-width="120"
        >
          <template slot-scope="scope">
            <el-tag :type="getUpgradeTypeColor(scope.row.upgradeType)">
              {{ getUpgradeTypeName(scope.row.upgradeType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="upgradePrice"
          :label="$t('user.grade.upgradeFee')"
          min-width="120"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.upgradeType === 1">Rp {{ scope.row.upgradePrice }}</span>
            <span v-else>{{ $t('user.grade.free') }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="isAvailable"
          :label="$t('user.grade.availableStatus')"
          min-width="100"
        >
          <template slot-scope="scope">
            <el-tag :type="scope.row.isAvailable ? 'success' : 'danger'">
              {{ scope.row.isAvailable ? $t('user.grade.available') : $t('user.grade.unavailable') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('user.grade.status')"
          min-width="100"
        >
          <template slot-scope="scope" v-if="checkPermi(['admin:system:user:level:use'])">
            <el-switch
              v-model="scope.row.isShow"
              :active-value="true"
              :inactive-value="false"
              :active-text="$t('user.grade.enable')"
              :inactive-text="$t('user.grade.disable')"
              disabled
              @click.native="onchangeIsShow(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column :label="$t('user.grade.operation')" min-width="120" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="edit(scope.row)" class="mr10" v-hasPermi="['admin:system:user:level:update']">{{ $t('user.grade.edit') }}</el-button>
            <el-button type="text" size="small" @click="handleDelete(scope.row.id, scope.$index)" v-hasPermi="['admin:system:user:level:delete']">{{ $t('user.grade.delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <creat-grade ref="grades" :user="userInfo"></creat-grade>
  </div>
</template>

<script>
  import { userListApi, groupListApi, levelListApi, levelUseApi, levelDeleteApi } from '@/api/user'
  import creatGrade from './creatGrade'
  import { checkPermi } from "@/utils/permission"; // 权限判断函数
  export default {
    name: 'Grade',
    filters: {
      typeFilter(status) {
        return this.$t(`user.grade.userTypes.${status}`) || status
      }
    },
    components: {creatGrade},
    data() {
      return {
        listLoading: true,
        userInfo:{},
        tableData: {
          data: [],
          total: 0,
        }
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      checkPermi,
      seachList() {
        this.getList()
      },
      add() {
        this.$refs.grades.dialogVisible = true
        this.userInfo = {};
      },
      edit(id) {
        // this.$refs.grades.info(id)
        this.userInfo = id;
        this.$refs.grades.dialogVisible = true
      },
      // 列表
      getList() {
        this.listLoading = true
        levelListApi().then(res => {
          this.tableData.data = res
          this.listLoading = false
        }).catch(() => {
          this.listLoading = false
        })
      },
      // 删除
      handleDelete(id, idx) {
        this.$modalSure(this.$t('user.grade.deleteConfirm')).then(() => {
          levelDeleteApi(id).then(() => {
            this.$message.success(this.$t('user.grade.deleteSuccess'))
            this.tableData.data.splice(idx, 1)
          })
        })
      },
      onchangeIsShow(row) {
        if(row.isShow == false){
          row.isShow = !row.isShow
          levelUseApi({id: row.id, isShow:row.isShow}).then(() => {
            this.$message.success(this.$t('user.grade.updateSuccess'))
            this.getList()
          }).catch(()=>{
            row.isShow = !row.isShow
          })
        }else{
          this.$modalSure(this.$t('user.grade.hideConfirm')).then(() => {
            row.isShow = !row.isShow
            levelUseApi({id: row.id, isShow:row.isShow}).then(() => {
              this.$message.success(this.$t('user.grade.updateSuccess'))
              this.getList()
            }).catch(()=>{
              row.isShow = !row.isShow
            })
          })
        }
      },
      // 获取升级方式名称
      getUpgradeTypeName(type) {
        return this.$t(`user.grade.upgradeTypes.${type}`) || this.$t('common.unknown')
      },
      // 获取升级方式颜色
      getUpgradeTypeColor(type) {
        const colorMap = {
          0: 'success',
          1: 'warning',
          2: 'info',
          3: 'primary'
        }
        return colorMap[type] || 'info'
      }
    }
  }
</script>

<style scoped lang="scss">
  .el-switch.is-disabled {
    opacity: 1;
  }
  ::v-deep .el-switch__label {
    cursor: pointer !important;;
  }
</style>
