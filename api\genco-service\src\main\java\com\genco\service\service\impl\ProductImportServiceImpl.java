package com.genco.service.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.genco.common.constants.Constants;
import com.genco.common.constants.SysConfigConstants;
import com.genco.common.exception.CrmebException;
import com.genco.common.model.product.StoreProduct;
import com.genco.common.model.brand.StoreBrand;
import com.genco.common.utils.DateUtil;
import com.genco.service.service.ProductImportService;
import com.genco.service.service.StoreProductService;
import com.genco.service.service.SystemConfigService;
import com.genco.service.service.TikTokTokenRefreshService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tiktokshop.open.sdk_java.api.AffiliateCreatorV202501Api;
import tiktokshop.open.sdk_java.invoke.ApiClient;
import tiktokshop.open.sdk_java.invoke.ApiResponse;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202501.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 商品导入服务实现类
 */
@Slf4j
@Service
public class ProductImportServiceImpl implements ProductImportService {

    @Autowired
    private StoreProductService storeProductService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private TikTokTokenRefreshService tikTokTokenRefreshService;

    @Override
    public List<StoreProduct> importProductsFromTikTok(String productId) {
        String appKey = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_APP_KEY);
        if (StrUtil.isBlank(appKey)) {
            throw new CrmebException("TikTok appId未设置");
        }
        String appSecret = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_APP_SECRET);
        if (StrUtil.isBlank(appSecret)) {
            throw new CrmebException("TikTok secret未设置");
        }
        String accessToken = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_ACCESS_TOKEN);
        if (StrUtil.isBlank(accessToken)) {
            throw new CrmebException("TikTok secret未设置");
        }
        BigDecimal platformCashBackRate = new BigDecimal(systemConfigService.getValueByKey(Constants.PLATFORM_CASH_BACK_RATE));
        ApiClient apiClient = new ApiClient();
        apiClient.setAppkey(appKey);
        apiClient.setSecret(appSecret);
        apiClient.setTokens(accessToken);

        AffiliateCreatorV202501Api affiliateCreatorV202501Api = new AffiliateCreatorV202501Api(apiClient);
        ApiResponse<GenerateAffiliateSharingLinkResponse> responseInfo = null;
        ApiResponse<CreatorSelectAffiliateProductResponse> resp = null;

        try {
            CreatorSelectAffiliateProductRequestBody requestBody = new CreatorSelectAffiliateProductRequestBody();
            CreatorSelectAffiliateProductRequestBodyFilterParams params = new CreatorSelectAffiliateProductRequestBodyFilterParams();
            List<String> productIds = new ArrayList<>();
            productIds.add(productId);
            params.setProductIds(productIds);
            requestBody.setFilterParams(params);
            resp = affiliateCreatorV202501Api.affiliateCreator202501SelectionProductsSearchPostWithHttpInfo(
                    apiClient.getTokens(), "application/json", null, 20, requestBody);

            String nextPageToken;
            List<CreatorSelectAffiliateProductResponseDataProducts> products;
            List<StoreProduct> storeProductList = new ArrayList<>();
            Integer totalCount;
            if (resp != null && resp.getStatusCode() == 200) {
                CreatorSelectAffiliateProductResponseData respData = resp.getData().getData();
                products = respData.getProducts();
                if (products != null && products.size() > 0) {
                    // 批量查重优化
                    List<String> outProductIds = products.stream().map(CreatorSelectAffiliateProductResponseDataProducts::getId).collect(Collectors.toList());
                    List<StoreProduct> existProducts = storeProductService.getByOutProductIds(outProductIds);
                    Set<String> existOutProductIdSet = existProducts.stream().map(StoreProduct::getOutProductId).collect(Collectors.toSet());
                    Map<String, StoreProduct> existProductsMap = existProducts.stream().collect(Collectors.toMap(StoreProduct::getOutProductId, p -> p));
                    List<StoreProduct> toSaveList = new ArrayList<>();
                    for (CreatorSelectAffiliateProductResponseDataProducts product : products) {
                        if (existOutProductIdSet.contains(product.getId())) {
                            // 已存在，查库得到StoreProduct并加入返回列表
                            StoreProduct exist = existProductsMap.get(product.getId());
                            if (exist != null) {
                                exist.setUserCashBackRate(exist.getCashBackRate().multiply(platformCashBackRate));
                                exist.setUserCashBackAmount(exist.getCashBackAmount().multiply(platformCashBackRate));
                                storeProductList.add(exist);
                            }
                            continue;
                        }
                        StoreProduct storeProduct = new StoreProduct();
                        storeProduct.setOutProductId(product.getId());
                        storeProduct.setChannel(SysConfigConstants.PRODUCT_CHANNEL_TIKTOK);
                        storeProduct.setImage(product.getMainImageUrl());
                        storeProduct.setBrand(product.getBrandName());
                        List<String> slideImages = new ArrayList<>();
                        slideImages.add(product.getMainImageUrl());
                        storeProduct.setSliderImage(JSONUtil.toJsonStr(slideImages));
                        storeProduct.setStoreName(product.getTitle());
                        if (product.getCommission() != null && product.getCommission().getRate() != null) {
                            storeProduct.setCashBackRate(BigDecimal.valueOf(product.getCommission().getRate()).multiply(new BigDecimal("0.0001")));
                            storeProduct.setUserCashBackRate(storeProduct.getCashBackRate().multiply(platformCashBackRate));
                        }
                        if (product.getCommission() != null && product.getCommission().getAmount() != null) {
                            storeProduct.setCashBackAmount(new BigDecimal(product.getCommission().getAmount().replace(".", "")));
                            storeProduct.setUserCashBackAmount(storeProduct.getCashBackAmount().multiply(platformCashBackRate));
                        }

                        if (product.getPrice() != null && product.getPrice().getCeilingPrice() != null) {
                            storeProduct.setMaxSalesPrice(new BigDecimal(product.getPrice().getCeilingPrice()));
                        }
                        if (product.getPrice() != null && product.getPrice().getFloorPrice() != null) {
                            storeProduct.setMinSalesPrice(new BigDecimal(product.getPrice().getFloorPrice()));
                            storeProduct.setPrice(new BigDecimal(product.getPrice().getFloorPrice()));
                            storeProduct.setSalesPrice(new BigDecimal(product.getPrice().getFloorPrice()));
                        }
                        if (product.getShop() != null && product.getShop().getName() != null) {
                            storeProduct.setShopName(product.getShop().getName());
                        }
                        storeProduct.setStoreInfo(product.getTitle());
                        storeProduct.setKeyword(product.getTitle());
                        storeProduct.setCateId("1");
                        storeProduct.setUnitName("0");
                        storeProduct.setIsBest(Boolean.TRUE);
                        if (product.getStock() != null && product.getStock().getQuantity() != null) {
                            storeProduct.setStock(product.getStock().getQuantity());
                        } else {
                            storeProduct.setStock(0);
                        }
                        storeProduct.setAddTime(DateUtil.getSecondTimestamp());
                        storeProduct.setFlatPattern("0");
                        storeProduct.setIsShow(false);
                        storeProduct.setIsHot(false);
                        storeProduct.setIsBenefit(true);
                        toSaveList.add(storeProduct);
                        storeProductList.add(storeProduct);
                    }
                    if (!toSaveList.isEmpty()) {
                        storeProductService.saveBatch(toSaveList);
                    }
                }
            }
            log.info("TikTok商品导入完成，共导入{}个商品", storeProductList.size());
            return storeProductList;
        } catch (Exception e) {
            log.error("TikTok商品导入失败", e);
            throw new CrmebException("TikTok商品导入失败: " + e.getMessage());
        }
    }

    @Override
    public List<StoreProduct> importProductsFromTikTok(List<String> productIds, StoreBrand brand, String operationUser, Consumer<StoreProduct> historyCallback) {
        List<StoreProduct> allImportedProducts = new ArrayList<>();

        for (String productId : productIds) {
            try {
                List<StoreProduct> importedProducts = importProductsFromTikTok(productId);
                allImportedProducts.addAll(importedProducts);

                // 如果提供了历史记录回调，为每个导入的商品调用回调
                if (historyCallback != null) {
                    for (StoreProduct product : importedProducts) {
                        historyCallback.accept(product);
                    }
                }
            } catch (Exception e) {
                log.error("导入商品失败，商品ID: {}, 错误: {}", productId, e.getMessage(), e);
                // 继续处理其他商品，不中断整个批量导入过程
            }
        }

        return allImportedProducts;
    }

    @Override
    public void refreshTikTokToken() {
        try {
            Integer nowTimeStamp = DateUtil.getNowTime();
            Integer accessExpireIn = Integer.valueOf(systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_TOKEN_EXPIRE_IN));
            //当失效时间达到一定阈值内，执行刷新动作
            if (accessExpireIn - nowTimeStamp < 100000) {
                // 使用统一的token刷新服务
                boolean success = tikTokTokenRefreshService.refreshAndUpdateToken();
                if (success) {
                    log.info("TikTok访问令牌刷新成功");
                } else {
                    log.error("TikTok访问令牌刷新失败");
                }
            }
        } catch (Exception e) {
            log.error("TikTok令牌刷新任务执行失败", e);
        }
    }
}
