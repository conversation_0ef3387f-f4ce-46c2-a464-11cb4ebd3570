<template>
  <el-form :model="ruleForm" ref="ruleForm" label-width="100px" class="demo-ruleForm" >
    <el-form-item>
      <el-alert :title="$t('user.levelUpgrade.changeWarning')" type="warning"></el-alert>
    </el-form-item>
    <el-form-item :label="$t('user.center.userLevel')" label-width="100px">
      <el-select v-model="ruleForm.levelId" clearable :placeholder="$t('common.pleaseSelect')" @change="currentSel">
        <el-option
          v-for="item in levelList"
          :key="item.grade"
          :label="item.name"
          :value="item.id"
        >
          <span style="float: left">{{ item.name }}</span>
          <span style="float: right; color: #8492a6; font-size: 13px">
            {{ getUpgradeTypeText(item.upgradeType) }}
            <span v-if="item.upgradeType === 1"> - Rp {{ item.upgradePrice }}</span>
          </span>
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item :label="$t('user.levelUpgrade.deductExperience')" label-width="100px" v-if="grade =='' ? false : grade < levelInfo.gradeLevel">
      <el-switch v-model="ruleForm.isSub"></el-switch>
    </el-form-item>
    <el-form-item>
      <el-button @click="resetForm('ruleForm')">{{ $t('common.cancel') }}</el-button>
      <el-button type="primary" @click="submitForm('ruleForm')">{{ $t('common.confirm') }}</el-button>
    </el-form-item>
  </el-form>
</template>
<script>
  import { userLevelUpdateApi} from '@/api/user'
  import {Debounce} from '@/utils/validate'
  export default {
    props:{
      levelInfo:{
        type:Object,
        default:{},
      },
      levelList:{
        type:Array,
        default:[]
      }
    },
    data() {
      return {
        grade:'',
        levelStatus:false,
        ruleForm: {
          isSub: false,
          levelId:"",
          uid:this.levelInfo.uid
        },
      };
    },
    created(){
      this.ruleForm.levelId = this.levelInfo.level?Number(this.levelInfo.level):''
    },
    watch: {
      levelInfo(val){
        this.ruleForm.uid = val.uid || 0;
        this.ruleForm.levelId = this.levelInfo.level?Number(this.levelInfo.level):val.levelId;
      },
    },
    methods: {
      submitForm:Debounce(function(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            userLevelUpdateApi(this.ruleForm).then(res=>{
              this.$message.success('编辑成功');
              this.$parent.$parent.getList();
              this.$parent.$parent.levelVisible = false;
              this.$refs[formName].resetFields()
              this.grade = '';
            })
          } else {
            return false;
          }
        });
      }),
      currentSel(){
        this.levelList.forEach(item=>{
          if(item.id == this.ruleForm.levelId){
            this.grade = item.grade;
          }
        })
      },
      resetForm(formName) {
        this.$nextTick(() => {
          this.$refs[formName].resetFields();
          this.grade = '';
        })
        this.$parent.$parent.levelVisible = false
      },
      // 获取升级方式文本
      getUpgradeTypeText(type) {
        return this.$t(`user.grade.upgradeTypes.${type}`) || this.$t('common.unknown')
      }
    },
  };
</script>
