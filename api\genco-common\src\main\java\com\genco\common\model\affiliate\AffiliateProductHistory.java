package com.genco.common.model.affiliate;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 联盟商品历史记录表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eb_affiliate_product_history")
@ApiModel(value = "AffiliateProductHistory对象", description = "联盟商品历史记录表")
public class AffiliateProductHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    // 状态常量
    public static final int STATUS_PENDING = 0;    // 待处理
    public static final int STATUS_IMPORTED = 1;   // 已入库
    public static final int STATUS_DELETED = 2;    // 已删除
    public static final int STATUS_FAILED = 3;     // 入库失败

    // 操作类型常量
    public static final String OPERATION_IMPORT = "IMPORT";   // 导入
    public static final String OPERATION_DELETE = "DELETE";   // 删除
    public static final String OPERATION_UPDATE = "UPDATE";   // 更新

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "TikTok商品ID")
    private String productId;

    @ApiModelProperty(value = "商品标题")
    private String title;

    @ApiModelProperty(value = "商品主图URL")
    private String mainImageUrl;

    @ApiModelProperty(value = "商品详情链接")
    private String detailLink;

    @ApiModelProperty(value = "销售区域")
    private String saleRegion;

    @ApiModelProperty(value = "是否有库存")
    private Boolean hasInventory;

    @ApiModelProperty(value = "销量")
    private Integer unitsSold;

    // 价格信息字段
    @ApiModelProperty(value = "原价最低价格")
    private String originalPriceMin;

    @ApiModelProperty(value = "原价最高价格")
    private String originalPriceMax;

    @ApiModelProperty(value = "原价货币单位")
    private String originalPriceCurrency;

    @ApiModelProperty(value = "销售价最低价格")
    private String salesPriceMin;

    @ApiModelProperty(value = "销售价最高价格")
    private String salesPriceMax;

    @ApiModelProperty(value = "销售价货币单位")
    private String salesPriceCurrency;

    // 佣金信息字段
    @ApiModelProperty(value = "佣金率（基点，100基点=1%）")
    private Integer commissionRate;

    @ApiModelProperty(value = "预计佣金金额")
    private String commissionAmount;

    @ApiModelProperty(value = "佣金货币单位")
    private String commissionCurrency;

    // 分类信息字段
    @ApiModelProperty(value = "分类ID")
    private String categoryId;

    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    @ApiModelProperty(value = "是否叶子分类")
    private Boolean categoryIsLeaf;

    @ApiModelProperty(value = "父分类ID")
    private String categoryParentId;

    // 店铺信息字段
    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    // 操作信息字段
    @ApiModelProperty(value = "状态：0-待处理，1-已入库，2-已删除，3-入库失败")
    private Integer status;

    @ApiModelProperty(value = "操作类型：IMPORT-导入，DELETE-删除，UPDATE-更新")
    private String operationType;

    @ApiModelProperty(value = "操作用户")
    private String operationUser;

    @ApiModelProperty(value = "操作时间")
    private Date operationTime;

    @ApiModelProperty(value = "错误信息（入库失败时记录）")
    private String errorMessage;

    @ApiModelProperty(value = "入库时间")
    private Date importTime;

    @ApiModelProperty(value = "删除时间")
    private Date deleteTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
