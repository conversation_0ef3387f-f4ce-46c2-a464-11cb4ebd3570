package com.genco.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.genco.common.model.affiliate.AffiliateProductHistoryV2;
import com.genco.common.model.product.StoreProduct;
import com.genco.common.page.CommonPage;
import com.genco.common.request.AffiliateProductImportRequest;
import com.genco.common.request.PageParamRequest;
import com.genco.common.response.AffiliateProductImportResponse;
import com.genco.common.response.ProductShareLinkResponse;
import com.genco.service.dao.AffiliateProductHistoryV2Dao;
import com.genco.service.service.AffiliateProductHistoryV2Service;
import com.genco.service.service.ProductImportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202501.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 联盟商品历史记录服务实现类 V2
 */
@Slf4j
@Service
public class AffiliateProductHistoryV2ServiceImpl
        extends ServiceImpl<AffiliateProductHistoryV2Dao, AffiliateProductHistoryV2>
        implements AffiliateProductHistoryV2Service {

    @Autowired
    private ProductImportService productImportService;

    @Override
    public void syncTikTokProducts(List<CreatorSelectAffiliateProductResponseDataProducts> products, Map<String, ProductShareLinkResponse> shareLinkResponseMap) {
        if (products == null || products.isEmpty()) {
            return;
        }

        try {
            List<AffiliateProductHistoryV2> historyList = products.stream()
                    .map(p -> this.convertToHistory(p, shareLinkResponseMap))
                    .collect(Collectors.toList());

            // 获取所有产品ID
            List<String> productIds = historyList.stream()
                    .map(AffiliateProductHistoryV2::getProductId)
                    .collect(Collectors.toList());

            // 查询已存在的记录
            List<AffiliateProductHistoryV2> existingRecords = this.getByProductIds(productIds);

            // 创建已存在产品ID的集合，用于快速查找
            List<String> existingProductIds = existingRecords.stream()
                    .map(AffiliateProductHistoryV2::getProductId)
                    .collect(Collectors.toList());

            // 分离新增和更新的记录
            List<AffiliateProductHistoryV2> newRecords = new ArrayList<>();
            List<AffiliateProductHistoryV2> updateRecords = new ArrayList<>();

            for (AffiliateProductHistoryV2 history : historyList) {
                if (existingProductIds.contains(history.getProductId())) {
                    // 找到对应的已存在记录，设置ID和创建时间
                    AffiliateProductHistoryV2 existingRecord = existingRecords.stream()
                            .filter(r -> r.getProductId().equals(history.getProductId()))
                            .findFirst()
                            .orElse(null);
                    if (existingRecord != null) {
                        history.setId(existingRecord.getId());
                        history.setCreateTime(existingRecord.getCreateTime());
                        history.setUpdateTime(new Date());
                        updateRecords.add(history);
                    }
                } else {
                    newRecords.add(history);
                }
            }

            // 批量插入新记录
            if (!newRecords.isEmpty()) {
                this.saveBatch(newRecords);
                log.info("成功插入{}个新商品到历史记录表V2", newRecords.size());
            }

            // 批量更新已存在记录
            if (!updateRecords.isEmpty()) {
                this.updateBatchById(updateRecords);
                log.info("成功更新{}个已存在商品到历史记录表V2", updateRecords.size());
            }

            log.info("成功同步{}个商品到历史记录表V2（新增：{}，更新：{}）",
                    historyList.size(), newRecords.size(), updateRecords.size());
        } catch (Exception e) {
            log.error("同步商品到历史记录表V2失败", e);
            throw new RuntimeException("同步商品历史记录失败", e);
        }
    }

    @Override
    public AffiliateProductHistoryV2 getByProductId(String productId) {
        LambdaQueryWrapper<AffiliateProductHistoryV2> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AffiliateProductHistoryV2::getProductId, productId)
                .orderByDesc(AffiliateProductHistoryV2::getCreateTime)
                .last("LIMIT 1");
        return this.getOne(wrapper);
    }

    @Override
    public List<AffiliateProductHistoryV2> getByProductIds(List<String> productIds) {
        if (productIds == null || productIds.isEmpty()) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<AffiliateProductHistoryV2> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(AffiliateProductHistoryV2::getProductId, productIds)
                .orderByDesc(AffiliateProductHistoryV2::getCreateTime);
        return this.list(wrapper);
    }

    /**
     * 转换TikTok商品数据为历史记录
     */
    private AffiliateProductHistoryV2 convertToHistory(CreatorSelectAffiliateProductResponseDataProducts product, Map<String, ProductShareLinkResponse> shareLinkResponseMap) {
        AffiliateProductHistoryV2 history = new AffiliateProductHistoryV2();

        // 基础信息
        history.setProductId(product.getId());
        history.setTitle(product.getTitle());
        history.setMainImageUrl(product.getMainImageUrl());
        history.setBrandName(product.getBrandName());

        if (shareLinkResponseMap != null && shareLinkResponseMap.get(history.getProductId()) != null) {
            history.setAffiliateShareLink(shareLinkResponseMap.get(history.getProductId()).getShareLink());
            history.setShareLinkTags(shareLinkResponseMap.get(history.getProductId()).getTags());
        }

        // 价格信息
        if (product.getPrice() != null) {
            if (product.getPrice().getFloorPrice() != null) {
                history.setFloorPrice(product.getPrice().getFloorPrice());
            }
            if (product.getPrice().getCeilingPrice() != null) {
                history.setCeilingPrice(product.getPrice().getCeilingPrice());
            }
            history.setCurrencyName(product.getPrice().getCurrency());
        }

        // 佣金信息
        if (product.getCommission() != null) {
            if (product.getCommission().getRate() != null) {
                history.setCommissionRate(product.getCommission().getRate());
            }
            if (product.getCommission().getAmount() != null) {
                history.setCommissionAmount(product.getCommission().getAmount());
            }
        }

        // 评价信息
        if (product.getReview() != null) {
            if (product.getReview().getCount() != null) {
                history.setReviewCount(String.valueOf(product.getReview().getCount()));
            }
            if (product.getReview().getOverallScore() != null) {
                history.setReviewOverallScore(product.getReview().getOverallScore());
            }
        }

        // 店铺信息
        if (product.getShop() != null) {
            history.setShopName(product.getShop().getName());
            history.setShopLogoUrl(product.getShop().getLogoUrl());
            history.setShopRating(product.getShop().getRating());
        }

        // 库存信息
        if (product.getStock() !=  null) {
            history.setStockQuantity(product.getStock().getQuantity());
        }
        if (product.getMarketPerformance() != null) {
            history.setHistoricalSoldQuantity(product.getMarketPerformance().getHistoricalSoldQuantity()); // 默认有库存，实际需要根据API响应调整
        }

        // V2版本新增字段
        history.setOperationType("SEARCH");
        history.setStatus(AffiliateProductHistoryV2.STATUS_PENDING); // 默认待处理状态
        history.setCreateTime(new Date());
        history.setUpdateTime(new Date());

        return history;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AffiliateProductImportResponse importProducts(AffiliateProductImportRequest request) {
        log.info("开始批量导入联盟商品V2，商品数量：{}", request.getProductIds().size());

        AffiliateProductImportResponse response = new AffiliateProductImportResponse();
        response.setTotalCount(request.getProductIds().size());
        response.setSuccessCount(0);
        response.setFailedCount(0);
        response.setSkippedCount(0);
        response.setSuccessProductIds(new ArrayList<>());
        response.setFailedProducts(new ArrayList<>());

        // 检查已入库的商品（状态为1的商品）
        List<String> alreadyImportedProductIds = checkAlreadyImportedProducts(request.getProductIds());
        response.setSkippedCount(alreadyImportedProductIds.size());

        // 过滤出需要导入的商品ID
        List<String> toImportProductIds = request.getProductIds().stream()
                .filter(id -> !alreadyImportedProductIds.contains(id))
                .collect(Collectors.toList());

        if (toImportProductIds.isEmpty()) {
            log.info("所有商品都已入库，无需导入");
            return response;
        }

        // 使用ProductImportService进行批量导入
        for (String productId : toImportProductIds) {
            try {
                // 调用现有的ProductImportService导入单个商品
                // TODO: 这里需要根据实际的ProductImportService接口调整
                 List<StoreProduct> importedProducts = productImportService.importProductsFromTikTok(productId);

                 if (!importedProducts.isEmpty()) {
                     StoreProduct importedProduct = importedProducts.get(0);
                     // 更新商品入库状态
                     Boolean updateResult = updateProductImportStatus(productId, request.getOperationUser());
                     if (updateResult) {
                         response.setSuccessCount(response.getSuccessCount() + 1);
                         response.getSuccessProductIds().add(productId);
                         log.info("商品导入成功，TikTok商品ID：{}，本地商品ID：{}", productId, importedProduct.getId());
                     } else {
                         response.setFailedCount(response.getFailedCount() + 1);
                         AffiliateProductImportResponse.FailedProductInfo failedInfo = new AffiliateProductImportResponse.FailedProductInfo();
                         failedInfo.setProductId(productId);
                         failedInfo.setErrorMessage("更新历史记录状态失败");
                         response.getFailedProducts().add(failedInfo);
                     }
                 } else {
                     response.setFailedCount(response.getFailedCount() + 1);
                     AffiliateProductImportResponse.FailedProductInfo failedInfo = new AffiliateProductImportResponse.FailedProductInfo();
                     failedInfo.setProductId(productId);
                     failedInfo.setErrorMessage("导入到本地数据库失败");
                     response.getFailedProducts().add(failedInfo);
                 }
            } catch (Exception e) {
                log.error("商品导入失败，商品ID：{}，错误：{}", productId, e.getMessage());

                response.setFailedCount(response.getFailedCount() + 1);
                AffiliateProductImportResponse.FailedProductInfo failedInfo = new AffiliateProductImportResponse.FailedProductInfo();
                failedInfo.setProductId(productId);
                failedInfo.setErrorMessage(e.getMessage());
                response.getFailedProducts().add(failedInfo);
            }
        }

        log.info("批量导入完成V2，总数：{}，成功：{}，失败：{}，跳过：{}",
                response.getTotalCount(), response.getSuccessCount(), response.getFailedCount(), response.getSkippedCount());

        return response;
    }

    @Override
    public Boolean markProductsAsDeleted(List<String> productIds, String operationUser) {
        if (CollectionUtils.isEmpty(productIds)) {
            return false;
        }

        try {
            // 批量更新删除状态
            boolean allSuccess = true;
            for (String productId : productIds) {
                Boolean result = updateProductDeleteStatus(productId, operationUser);
                if (!result) {
                    allSuccess = false;
                    log.error("更新商品删除状态失败V2，商品ID：{}", productId);
                }
            }

            return allSuccess;
        } catch (Exception e) {
            log.error("标记商品为已删除失败V2", e);
            return false;
        }
    }

    @Override
    public CommonPage<AffiliateProductHistoryV2> getHistoryList(Integer status, Integer brandId, PageParamRequest pageParamRequest) {
        log.info("查询商品历史记录V2，状态：{}，品牌ID：{}", status, brandId);

        Page<AffiliateProductHistoryV2> page = new Page<>(pageParamRequest.getPage(), pageParamRequest.getLimit());

        LambdaQueryWrapper<AffiliateProductHistoryV2> wrapper = new LambdaQueryWrapper<>();

        // 状态筛选
        if (status != null) {
            wrapper.eq(AffiliateProductHistoryV2::getStatus, status);
        }

        // 按创建时间倒序排列
        wrapper.orderByDesc(AffiliateProductHistoryV2::getCreateTime);

        Page<AffiliateProductHistoryV2> result = this.page(page, wrapper);

        // 手动构建 CommonPage
        CommonPage<AffiliateProductHistoryV2> commonPage = new CommonPage<>();
        commonPage.setPage((int) result.getCurrent());
        commonPage.setLimit((int) result.getSize());
        commonPage.setTotal(result.getTotal());
        commonPage.setTotalPage((int) result.getPages());
        commonPage.setList(result.getRecords());

        return commonPage;
    }

    @Override
    public List<String> checkExistingProducts(List<String> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<AffiliateProductHistoryV2> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(AffiliateProductHistoryV2::getProductId, productIds)
                .select(AffiliateProductHistoryV2::getProductId);

        List<AffiliateProductHistoryV2> existingRecords = this.list(wrapper);

        return existingRecords.stream()
                .map(AffiliateProductHistoryV2::getProductId)
                .collect(Collectors.toList());
    }

    /**
     * 检查已入库的商品（状态为1的商品）
     */
    private List<String> checkAlreadyImportedProducts(List<String> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<AffiliateProductHistoryV2> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(AffiliateProductHistoryV2::getProductId, productIds)
                .eq(AffiliateProductHistoryV2::getStatus, AffiliateProductHistoryV2.STATUS_IMPORTED)
                .select(AffiliateProductHistoryV2::getProductId);

        List<AffiliateProductHistoryV2> importedRecords = this.list(wrapper);

        return importedRecords.stream()
                .map(AffiliateProductHistoryV2::getProductId)
                .collect(Collectors.toList());
    }

    /**
     * 更新商品入库状态
     */
    private Boolean updateProductImportStatus(String productId, String operationUser) {
        try {
            AffiliateProductHistoryV2 history = getByProductId(productId);
            if (history != null) {
                history.setStatus(AffiliateProductHistoryV2.STATUS_IMPORTED);
                history.setOperationType("IMPORT");
                history.setOperationUser(operationUser);
                history.setUpdateTime(new Date());
                return this.updateById(history);
            }
            return false;
        } catch (Exception e) {
            log.error("更新商品入库状态失败V2，商品ID：{}，错误：{}", productId, e.getMessage());
            return false;
        }
    }

    /**
     * 更新商品删除状态
     */
    public Boolean updateProductDeleteStatus(String productId, String operationUser) {
        try {
            AffiliateProductHistoryV2 history = getByProductId(productId);
            if (history != null) {
                history.setStatus(AffiliateProductHistoryV2.STATUS_DELETED);
                history.setOperationType("DELETE");
                history.setOperationUser(operationUser);
                history.setUpdateTime(new Date());
                return this.updateById(history);
            }
            return false;
        } catch (Exception e) {
            log.error("更新商品删除状态失败V2，商品ID：{}，错误：{}", productId, e.getMessage());
            return false;
        }
    }
}
