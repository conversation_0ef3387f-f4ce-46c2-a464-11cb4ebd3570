package ${package}.${moduleName}.dao;

import ${package}.${moduleName}.entity.${className}Entity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * ${comments} DAO 映射层
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * @author: ${author}
 * +----------------------------------------------------------------------
 * @date ${datetime}
 * +----------------------------------------------------------------------
 * @email ${email}
 * +----------------------------------------------------------------------
 */
@Mapper
public interface ${className}Dao extends BaseMapper<${className}Entity> {

}
