package com.genco.service.service;

import com.genco.common.model.affiliate.AffiliateProductHistoryV2;
import com.genco.common.page.CommonPage;
import com.genco.common.request.AffiliateProductImportRequest;
import com.genco.common.request.PageParamRequest;
import com.genco.common.response.AffiliateProductImportResponse;
import com.baomidou.mybatisplus.extension.service.IService;
import com.genco.common.response.ProductShareLinkResponse;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202501.CreatorSelectAffiliateProductResponseDataProducts;

import java.util.List;
import java.util.Map;

/**
 * 联盟商品历史记录服务接口 V2
 */
public interface AffiliateProductHistoryV2Service extends IService<AffiliateProductHistoryV2> {

    /**
     * 同步TikTok商品数据到历史记录表
     * @param products TikTok商品列表
     */
    void syncTikTokProducts(List<CreatorSelectAffiliateProductResponseDataProducts> products, Map<String, ProductShareLinkResponse> shareLinkResponseMap);

    /**
     * 根据商品ID查询历史记录
     * @param productId 商品ID
     * @return 历史记录
     */
    AffiliateProductHistoryV2 getByProductId(String productId);

    /**
     * 批量根据商品ID查询历史记录
     * @param productIds 商品ID列表
     * @return 历史记录列表
     */
    List<AffiliateProductHistoryV2> getByProductIds(List<String> productIds);

    /**
     * 批量导入联盟商品到本地数据库 V2
     *
     * @param request 导入请求
     * @return 导入结果
     */
    AffiliateProductImportResponse importProducts(AffiliateProductImportRequest request);

    /**
     * 标记商品为已删除状态 V2
     *
     * @param productIds 商品ID列表
     * @param operationUser 操作用户
     * @return 是否成功
     */
    Boolean markProductsAsDeleted(List<String> productIds, String operationUser);

    /**
     * 分页查询历史记录 V2
     *
     * @param status 状态筛选
     * @param brandId 品牌ID筛选（已废弃，保留参数兼容性）
     * @param pageParamRequest 分页参数
     * @return 分页结果
     */
    CommonPage<AffiliateProductHistoryV2> getHistoryList(Integer status, Integer brandId, PageParamRequest pageParamRequest);

    /**
     * 检查商品是否已存在 V2
     *
     * @param productIds 商品ID列表
     * @return 已存在的商品ID列表
     */
    List<String> checkExistingProducts(List<String> productIds);
}
