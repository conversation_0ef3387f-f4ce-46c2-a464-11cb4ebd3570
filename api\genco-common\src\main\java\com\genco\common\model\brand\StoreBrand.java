package com.genco.common.model.brand;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 品牌表
 *
 * 品牌状态使用规范：
 * - 新增品牌：默认 "0"(待上架)
 * - 审核通过：从 "0" → "1"(已上架)
 * - 临时下架：从 "1" → "2"(已下架)
 * - 重新上架：从 "2" → "1"(已上架)
 * - 逻辑删除：任何状态 → "-1"(已删除)
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eb_store_brand")
@ApiModel(value = "StoreBrand对象", description = "品牌表")
public class StoreBrand implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 品牌状态常量 */
    public static final String STATUS_DELETED = "-1";    // 已删除
    public static final String STATUS_OFFLINE = "0";     // 待上架
    public static final String STATUS_ONLINE = "1";      // 已上架
    public static final String STATUS_SUSPENDED = "2";   // 已下架

    @ApiModelProperty(value = "品牌ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "logo地址")
    private String logoUrl;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "状态：-1=已删除，0=待上架，1=已上架，2=已下架")
    private String status;

    @ApiModelProperty(value = "优先级")
    private Integer priority;

    @ApiModelProperty(value = "最高返现率")
    private BigDecimal maxCashBackRate;

    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "修改时间")
    private Date gmtModified;

    @ApiModelProperty(value = "所属行业")
    private String industry;

    @ApiModelProperty(value = "入驻电商平台")
    private String platform;

    @ApiModelProperty(value = "商品数量")
    private Integer productCount;

    @ApiModelProperty(value = "商品已售数量")
    private Integer productSoldCount;

    @ApiModelProperty(value = "商品已售金额")
    private BigDecimal productSoldAmount;

    @ApiModelProperty(value = "商品已返现金额")
    private BigDecimal productCashbackAmount;

    @ApiModelProperty(value = "商品分享数量")
    private Integer productShareCount;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "是否热卖品牌")
    private Boolean isHot;

    @ApiModelProperty(value = "是否高返现品牌")
    private Boolean isHighCashback;

    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    @ApiModelProperty(value = "联系人电话")
    private String contactPhone;
}
