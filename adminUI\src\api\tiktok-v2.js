import request from '@/utils/request'

/**
 * TikTok Shop API V2相关接口
 */

/**
 * 联盟选品查询（V2版本）
 * @param {Object} params 查询参数 - 与AffiliateProductSearchRequestV2保持一致
 * @param {Number} params.pageSize 每页数量，默认20，最大100
 * @param {String} params.pageToken 分页令牌，用于获取下一页数据
 * @param {Array} params.productIds 商品ID列表
 * @param {String} params.titleKeyword 商品标题关键词，用于模糊搜索
 * @param {Array} params.categoryIds 分类ID列表
 * @param {Number} params.commissionRateGe 最低佣金率（基点，1% = 100基点）
 * @param {Number} params.commissionRateLe 最高佣金率（基点，1% = 100基点）
 * @param {Number} params.priceRangeGe 最低价格
 * @param {Number} params.priceRangeLe 最高价格
 * @param {Number} params.shopRatingGe 最低店铺评分
 * @param {Number} params.shopRatingLe 最高店铺评分
 * @param {Number} params.soldQuantityGe 最低销量
 * @param {Number} params.soldQuantityLe 最高销量
 * @param {Array} params.poolIds 商品池ID列表
 * @param {String} params.sortType 排序类型：COMMISSION_RATE-佣金率，PRICE-价格，SOLD_QUANTITY-销量
 */
export function searchAffiliateProductsV2(params) {
  return request({
    url: '/v2/admin/affiliate/products/search',
    method: 'post',
    data: params
  })
}

/**
 * 批量导入联盟商品（V2版本）
 * @param {Object} data 导入请求数据
 * @param {Array} data.productIds 商品ID列表
 * @param {Number} data.brandId 品牌ID
 * @param {String} data.operationUser 操作用户
 */
export function importAffiliateProductsV2(data) {
  return request({
    url: '/v2/admin/affiliate/products/import',
    method: 'post',
    data
  })
}

/**
 * 批量删除联盟商品（V2版本）
 * @param {Array} productIds 商品ID列表
 */
export function deleteAffiliateProductsV2(productIds) {
  return request({
    url: '/v2/admin/affiliate/products/delete',
    method: 'post',
    data: productIds
  })
}

/**
 * 查询商品操作历史（V2版本）
 * @param {Object} params 查询参数
 * @param {String} params.productId 商品ID
 * @param {Number} params.status 状态
 * @param {String} params.operationType 操作类型
 * @param {Number} params.page 页码
 * @param {Number} params.limit 每页数量
 */
export function getAffiliateProductHistoryV2(params) {
  return request({
    url: '/v2/admin/affiliate/products/history',
    method: 'get',
    params
  })
}

/**
 * 检查商品是否已存在（V2版本）
 * @param {Array} productIds 商品ID列表
 */
export function checkProductsExistV2(productIds) {
  return request({
    url: '/v2/admin/affiliate/products/check-existing',
    method: 'post',
    data: { productIds }
  })
}