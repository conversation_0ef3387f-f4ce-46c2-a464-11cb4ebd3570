package com.genco.common.token;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * TikTok用户授权返回数据
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "TikTokOauthToken对象", description = "TikTokOauthToken")
public class TikTokOauthToken implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("access_token")
    private String accessToken;

    @JsonProperty("access_token_expire_in")
    private Integer expiresIn;

    @JsonProperty("open_id")
    private String openId;

    @JsonProperty("seller_name")
    private String sellerName;

    @JsonProperty("user_type")
    private Integer userType;

    @JsonProperty("request_id")
    private String requestId;

    @JsonProperty("refresh_token_expire_in")
    private Integer refreshExpiresIn;

    @JsonProperty("refresh_token")
    private String refreshToken;

    private String scope;

    @JsonProperty("token_type")
    private String tokenType;

    @JsonProperty("error")
    private JSONObject error;
}
