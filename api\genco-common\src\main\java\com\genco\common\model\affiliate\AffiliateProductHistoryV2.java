package com.genco.common.model.affiliate;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 联盟商品历史记录表 V2
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eb_affiliate_product_history_v2")
@ApiModel(value = "AffiliateProductHistoryV2对象", description = "联盟商品历史记录表 V2")
public class AffiliateProductHistoryV2 implements Serializable {

    private static final long serialVersionUID = 1L;

    // 状态常量
    public static final int STATUS_PENDING = 0;    // 待处理
    public static final int STATUS_IMPORTED = 1;   // 已入库
    public static final int STATUS_DELETED = 2;    // 已删除
    public static final int STATUS_FAILED = 3;     // 入库失败

    // 操作类型常量
    public static final String OPERATION_IMPORT = "IMPORT";   // 导入
    public static final String OPERATION_DELETE = "DELETE";   // 删除
    public static final String OPERATION_UPDATE = "UPDATE";   // 更新

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "TikTok商品ID")
    private String productId;

    @ApiModelProperty(value = "商品标题")
    private String title;

    @ApiModelProperty(value = "商品主图URL")
    private String mainImageUrl;

    @ApiModelProperty(value = "商品品牌")
    private String brandName;

    // 价格信息字段
    @ApiModelProperty(value = "原价最低价格")
    private String floorPrice;

    @ApiModelProperty(value = "原价最高价格")
    private String ceilingPrice;

    @ApiModelProperty(value = "原价货币单位")
    private String currencyName;

    // 佣金信息字段
    @ApiModelProperty(value = "佣金率（基点，100基点=1%）")
    private Integer commissionRate;

    @ApiModelProperty(value = "预计佣金金额")
    private String commissionAmount;

    // 分享链接字段
    @ApiModelProperty(value = "联盟分享链接")
    private String affiliateShareLink;

    @ApiModelProperty(value = "分享链接标签")
    private String shareLinkTags;

    // 商品评分字段
    @ApiModelProperty(value = "The count of reviews")
    private String reviewCount;

    @ApiModelProperty(value = "The average score of the product, the range is (0,5]")
    private String reviewOverallScore;

    // 店铺信息字段
    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "店铺评分, (0,5]")
    private String shopRating;

    @ApiModelProperty(value = "The logo URL of the shop")
    private String shopLogoUrl;

    @ApiModelProperty(value = "The detailed stock quantity of the product")
    private Integer stockQuantity;

    @ApiModelProperty(value = "The total number of products sold in history")
    private Integer historicalSoldQuantity;

    // 操作信息字段
    @ApiModelProperty(value = "状态：0-待处理，1-已入库，2-已删除，3-入库失败")
    private Integer status;

    @ApiModelProperty(value = "操作类型：IMPORT-导入，DELETE-删除，UPDATE-更新")
    private String operationType;

    @ApiModelProperty(value = "操作用户")
    private String operationUser;

    @ApiModelProperty(value = "操作时间")
    private Date operationTime;

    @ApiModelProperty(value = "错误信息（入库失败时记录）")
    private String errorMessage;

    @ApiModelProperty(value = "入库时间")
    private Date importTime;

    @ApiModelProperty(value = "删除时间")
    private Date deleteTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
