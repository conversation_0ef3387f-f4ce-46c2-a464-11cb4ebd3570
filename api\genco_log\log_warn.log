{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:53.147",
                    "level": "WARN",
                    "thread": "main",
                    "class": "c.b.m.core.metadata.TableInfoHelper",
                    "message": "Warn: Could not find @TableId in Class: com.genco.common.vo.UserFundsMonitor." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:53.420",
                    "level": "WARN",
                    "thread": "main",
                    "class": "c.b.m.core.metadata.TableInfoHelper",
                    "message": "Warn: Could not find @TableId in Class: com.genco.common.model.order.StoreOrderStatus." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 09:19:53.720",
                    "level": "WARN",
                    "thread": "main",
                    "class": "c.b.m.core.metadata.TableInfoHelper",
                    "message": "Warn: Could not find @TableId in Class: com.genco.common.model.system.SystemRoleMenu." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:16:20.074",
                    "level": "WARN",
                    "thread": "main",
                    "class": "c.b.m.core.metadata.TableInfoHelper",
                    "message": "Warn: Could not find @TableId in Class: com.genco.common.vo.UserFundsMonitor." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:16:20.283",
                    "level": "WARN",
                    "thread": "main",
                    "class": "c.b.m.core.metadata.TableInfoHelper",
                    "message": "Warn: Could not find @TableId in Class: com.genco.common.model.order.StoreOrderStatus." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 10:16:20.546",
                    "level": "WARN",
                    "thread": "main",
                    "class": "c.b.m.core.metadata.TableInfoHelper",
                    "message": "Warn: Could not find @TableId in Class: com.genco.common.model.system.SystemRoleMenu." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 12:24:07.124",
                    "level": "WARN",
                    "thread": "main",
                    "class": "c.b.m.core.metadata.TableInfoHelper",
                    "message": "Warn: Could not find @TableId in Class: com.genco.common.vo.UserFundsMonitor." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 12:24:07.366",
                    "level": "WARN",
                    "thread": "main",
                    "class": "c.b.m.core.metadata.TableInfoHelper",
                    "message": "Warn: Could not find @TableId in Class: com.genco.common.model.order.StoreOrderStatus." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 12:24:07.623",
                    "level": "WARN",
                    "thread": "main",
                    "class": "c.b.m.core.metadata.TableInfoHelper",
                    "message": "Warn: Could not find @TableId in Class: com.genco.common.model.system.SystemRoleMenu." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:50:54.622",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7af51633] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:50:55.675",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@595e79fb] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:50:55.889",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@11d77886] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:50:57.361",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37234d66] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:50:57.440",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51f36194] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:51:02.996",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@673ddc67] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:51:03.127",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@38a31680] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:51:03.461",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@658ca105] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:51:04.187",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7def7e6d] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:51:04.402",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e66100e] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:51:04.403",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@50105bf7] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:51:06.368",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-8",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5aa90965] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:51:06.568",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2893eef2] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:51:07.286",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@46eb1d72] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:51:07.320",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3a1cd31f] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:51:51.130",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@635571e6] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:51:51.544",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4f8b3f36] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:55:20.767",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2ab8efb1] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:55:33.414",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@493684b0] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:55:36.710",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Failure in @ExceptionHandler com.genco.common.exception.GlobalExceptionHandler#defaultExceptionHandler(Exception)" }
                    
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:309)
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:272)
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:118)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:520)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1153)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:923)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:287)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:287)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:181)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:82)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:123)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:403)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:61)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:141)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1300)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1111)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1057)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:665)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.genco.admin.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:36)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method)
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93)
	at sun.nio.ch.IOUtil.write(IOUtil.java:65)
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:469)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:138)
	at org.apache.tomcat.util.net.NioBlockingSelector.write(NioBlockingSelector.java:101)
	at org.apache.tomcat.util.net.NioSelectorPool.write(NioSelectorPool.java:152)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1253)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:740)
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:693)
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:683)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:564)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:157)
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:217)
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1155)
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:398)
	at org.apache.coyote.Response.action(Response.java:209)
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:305)
	... 111 common frames omitted
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:55:37.505",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e344e9a] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:56:02.140",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1409032d] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:56:05.416",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@75fdbb61] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:56:33.182",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64a039e3] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:56:36.942",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Failure in @ExceptionHandler com.genco.common.exception.GlobalExceptionHandler#defaultExceptionHandler(Exception)" }
                    
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:309)
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:272)
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:118)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:520)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1153)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:923)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:287)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:287)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:181)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:82)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:123)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:403)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:61)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:141)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1300)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1111)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1057)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:665)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.genco.admin.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:36)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method)
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93)
	at sun.nio.ch.IOUtil.write(IOUtil.java:65)
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:469)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:138)
	at org.apache.tomcat.util.net.NioBlockingSelector.write(NioBlockingSelector.java:101)
	at org.apache.tomcat.util.net.NioSelectorPool.write(NioSelectorPool.java:152)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1253)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:740)
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:693)
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:683)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:564)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:157)
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:217)
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1155)
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:398)
	at org.apache.coyote.Response.action(Response.java:209)
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:305)
	... 111 common frames omitted
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:56:39.792",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c3e8477] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:56:42.104",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2b909da1] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:56:43.460",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Failure in @ExceptionHandler com.genco.common.exception.GlobalExceptionHandler#defaultExceptionHandler(Exception)" }
                    
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:309)
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:272)
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:118)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:520)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1153)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:923)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:287)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:287)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:181)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:82)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:123)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:403)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:61)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:141)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1300)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1111)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1057)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:665)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.genco.admin.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:36)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method)
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93)
	at sun.nio.ch.IOUtil.write(IOUtil.java:65)
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:469)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:138)
	at org.apache.tomcat.util.net.NioBlockingSelector.write(NioBlockingSelector.java:101)
	at org.apache.tomcat.util.net.NioSelectorPool.write(NioSelectorPool.java:152)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1253)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:740)
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:693)
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:683)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:564)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:157)
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:217)
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1155)
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:398)
	at org.apache.coyote.Response.action(Response.java:209)
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:305)
	... 111 common frames omitted
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:56:46.920",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@18ee4e60] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:56:47.125",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fd2c74b] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:56:54.694",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-21",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5c2eea89] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:57:07.258",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29e74120] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:57:20.816",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7f8a399b] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:57:21.633",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@32988789] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:57:47.029",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24266b19] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:57:50.218",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3eb325ff] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:58:01.254",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5a693a85] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:58:03.042",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@732f75d9] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:58:12.500",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-8",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b6b4592] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:58:22.325",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@169aa91e] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:58:24.251",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@723a47e4] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:58:53.601",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@185a082e] was not registered for synchronization because DataSource is not transactional" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-08 14:58:56.869",
                    "level": "WARN",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.s.s.i.AffiliateProductHistoryV2ServiceImpl",
                    "message": "SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66084ab3] was not registered for synchronization because DataSource is not transactional" }
                    
