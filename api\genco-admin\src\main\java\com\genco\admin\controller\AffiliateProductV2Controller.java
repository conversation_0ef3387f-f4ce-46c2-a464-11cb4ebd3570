package com.genco.admin.controller;

import com.genco.common.model.affiliate.AffiliateProductHistoryV2;
import com.genco.common.page.CommonPage;
import com.genco.common.request.AffiliateProductImportRequest;
import com.genco.common.request.AffiliateProductSearchRequestV2;
import com.genco.common.request.PageParamRequest;
import com.genco.common.response.AffiliateProductImportResponse;
import com.genco.common.response.AffiliateProductResponseV2;
import com.genco.common.response.CommonResult;
import com.genco.service.service.AffiliateProductHistoryV2Service;
import com.genco.service.service.AffiliateProductServiceV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tiktokshop.open.sdk_java.invoke.ApiException;

import java.util.List;

/**
 * 联盟选品控制器 V2
 */
@Slf4j
@RestController
@RequestMapping("api/v2/admin/affiliate/products")
@Api(tags = "联盟选品管理V2")
public class AffiliateProductV2Controller {

    @Autowired
    private AffiliateProductServiceV2 affiliateProductServiceV2;

    @Autowired
    private AffiliateProductHistoryV2Service affiliateProductHistoryV2Service;

    /**
     * 搜索联盟产品 V2
     */
    @ApiOperation("搜索联盟产品V2")
    @PostMapping("/search")
    public CommonResult<AffiliateProductResponseV2> searchProducts(
            @Validated @RequestBody AffiliateProductSearchRequestV2 request) {

        try {
            AffiliateProductResponseV2 response = affiliateProductServiceV2.searchProducts(request);
            log.info("联盟产品搜索V2成功，返回{}个产品",
                    response.getProducts() != null ? response.getProducts().size() : 0);
            return CommonResult.success(response);

        } catch (ApiException e) {
            log.error("联盟产品搜索V2失败，API异常: {}", e.getMessage(), e);

            String errorMessage = e.getMessage();
            if (errorMessage != null) {
                // 根据不同的错误类型返回友好的错误信息
                if (errorMessage.contains("400") || errorMessage.contains("Bad Request")) {
                    return CommonResult.failed("请求参数错误，请检查搜索条件");
                } else if (errorMessage.contains("401") || errorMessage.contains("Unauthorized")) {
                    return CommonResult.failed("TikTok API认证失败，请检查访问令牌");
                } else if (errorMessage.contains("403") || errorMessage.contains("Forbidden")) {
                    return CommonResult.failed("TikTok API权限不足，请检查应用权限配置");
                } else if (errorMessage.contains("429") || errorMessage.contains("rate limit")) {
                    return CommonResult.failed("TikTok API调用频率超限，请稍后重试");
                } else if (errorMessage.contains("配置")) {
                    return CommonResult.failed("TikTok API配置错误: " + errorMessage);
                }
            }

            return CommonResult.failed("联盟产品搜索V2失败: " + errorMessage);

        } catch (Exception e) {
            log.error("联盟产品搜索V2失败，系统异常: {}", e.getMessage(), e);
            return CommonResult.failed("系统异常，请稍后重试");
        }
    }

    /**
     * 获取API版本信息
     */
    @ApiOperation("获取API版本信息")
    @GetMapping("/version")
    public CommonResult<String> getVersion() {
        return CommonResult.success("V2 - 基于TikTok AffiliateCreatorV202501Api");
    }

    /**
     * 批量导入联盟商品到本地数据库 V2
     */
    @ApiOperation(value = "批量导入联盟商品V2", notes = "将选中的TikTok联盟商品导入到本地数据库V2")
    @PostMapping("/import")
    public CommonResult<AffiliateProductImportResponse> importProducts(@RequestBody @Validated AffiliateProductImportRequest request) {
        log.info("开始批量导入联盟商品V2，商品数量：{}（品牌信息将自动从TikTok API提取）", request.getProductIds().size());

        try {
            AffiliateProductImportResponse response = affiliateProductHistoryV2Service.importProducts(request);
            log.info("批量导入完成V2，总数：{}，成功：{}，失败：{}，跳过：{}",
                    response.getTotalCount(), response.getSuccessCount(), response.getFailedCount(), response.getSkippedCount());

            return CommonResult.success(response, "商品导入完成");
        } catch (Exception e) {
            log.error("批量导入联盟商品失败V2", e);
            return CommonResult.failed("商品导入失败: " + e.getMessage());
        }
    }

    /**
     * 标记商品为已删除状态 V2
     */
    @ApiOperation(value = "删除联盟商品V2", notes = "标记选中的联盟商品为已删除状态V2")
    @PostMapping("/delete")
    public CommonResult<Boolean> deleteProducts(@RequestBody List<String> productIds) {
        log.info("开始删除联盟商品V2，商品数量：{}", productIds.size());

        try {
            // TODO: 从当前登录用户获取操作用户信息
            String operationUser = "admin"; // 临时硬编码，实际应该从SecurityContext获取

            Boolean result = affiliateProductHistoryV2Service.markProductsAsDeleted(productIds, operationUser);

            if (result) {
                log.info("删除联盟商品成功V2，商品数量：{}", productIds.size());
                return CommonResult.success(result, "商品删除成功");
            } else {
                return CommonResult.failed("商品删除失败");
            }
        } catch (Exception e) {
            log.error("删除联盟商品失败V2", e);
            return CommonResult.failed("商品删除失败: " + e.getMessage());
        }
    }

    /**
     * 查询商品历史记录 V2
     */
    @ApiOperation(value = "查询商品历史记录V2", notes = "分页查询联盟商品的操作历史记录V2")
    @GetMapping("/history")
    public CommonResult<CommonPage<AffiliateProductHistoryV2>> getProductHistory(
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "brandId", required = false) Integer brandId,
            @Validated PageParamRequest pageParamRequest) {

        log.info("查询商品历史记录V2，状态：{}，品牌ID：{}", status, brandId);

        try {
            CommonPage<AffiliateProductHistoryV2> result = affiliateProductHistoryV2Service.getHistoryList(status, brandId, pageParamRequest);
            return CommonResult.success(result, "查询成功");
        } catch (Exception e) {
            log.error("查询商品历史记录失败V2", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }

    /**
     * 检查商品是否已存在 V2
     */
    @ApiOperation(value = "检查商品是否已存在V2", notes = "检查指定的商品ID是否已经在历史记录中存在V2")
    @PostMapping("/check-existing")
    public CommonResult<List<String>> checkExistingProducts(@RequestBody List<String> productIds) {
        log.info("检查商品是否已存在V2，商品数量：{}", productIds.size());

        try {
            List<String> existingProductIds = affiliateProductHistoryV2Service.checkExistingProducts(productIds);
            log.info("检查完成V2，已存在商品数量：{}", existingProductIds.size());

            return CommonResult.success(existingProductIds, "检查完成");
        } catch (Exception e) {
            log.error("检查商品是否已存在失败V2", e);
            return CommonResult.failed("检查失败: " + e.getMessage());
        }
    }
}
