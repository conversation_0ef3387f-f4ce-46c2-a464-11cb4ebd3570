<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.genco.service.dao.tiktok.OrderPullTaskDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.genco.service.model.tiktok.OrderPullTask">
        <id column="id" property="id" />
        <result column="platform" property="platform" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="batch_no" property="batchNo" />
        <result column="page_no" property="pageNo" />
        <result column="next_page_token" property="nextPageToken" />
        <result column="status" property="status" />
        <result column="retry_count" property="retryCount" />
        <result column="last_pull_time" property="lastPullTime" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, platform, start_time, end_time, batch_no, page_no, next_page_token, status, retry_count, last_pull_time, remark, create_time, update_time
    </sql>

</mapper> 