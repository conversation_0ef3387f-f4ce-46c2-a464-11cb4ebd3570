<template>
  <el-dialog
    v-if="dialogVisible"
    :title="$t('user.grade.form.dialogTitle')"
    :visible.sync="dialogVisible"
    width="500px"
    :before-close="handleClose" >
    <el-form :model="user" :rules="rules" ref="user" label-width="100px" class="demo-ruleForm" v-loading="loading">
      <el-form-item :label="$t('user.grade.form.levelNameLabel')" prop="name">
        <el-input v-model="user.name" :placeholder="$t('user.grade.form.levelNamePlaceholder')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('user.grade.form.gradeLabel')" prop="grade">
        <el-input  v-model.number="user.grade" :placeholder="$t('user.grade.form.gradePlaceholder')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('user.grade.form.discountLabel')" prop="discount">
        <el-input-number  :min="0" :max="100" step-strictly  v-model="user.discount" :placeholder="$t('user.grade.form.discountPlaceholder')"></el-input-number>
      </el-form-item>
      <el-form-item :label="$t('user.grade.form.experienceLabel')" prop="experience">
        <el-input-number  v-model.number="user.experience" :placeholder="$t('user.grade.form.experiencePlaceholder')" :min="0" step-strictly></el-input-number>
      </el-form-item>
      <el-form-item :label="$t('user.grade.form.iconLabel')" prop="icon">
        <div class="upLoadPicBox" @click="modalPicTap('1', 'icon')">
          <div v-if="user.icon" class="pictrue"><img :src="user.icon"></div>
          <div v-else-if="formValidate.icon" class="pictrue"><img :src="formValidate.icon"></div>
          <div v-else class="upLoad">
            <i class="el-icon-camera cameraIconfont" />
          </div>
        </div>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="resetForm('user')">{{ $t('user.grade.form.cancel') }}</el-button>
      <el-button type="primary" @click="submitForm('formValidate')" v-hasPermi="['admin:system:user:level:update','admin:system:user:level:save']">{{ $t('user.grade.form.confirm') }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import { levelSaveApi, levelInfoApi, levelUpdateApi } from '@/api/user'
  import {Debounce} from '@/utils/validate'
  const obj = {
    name:'',
    grade: 1,
    discount: '',
    icon: '',
    image: '',
    id: null
  }
  export default {
    name: "CreatGrade",
    props:{
      'user':Object
    },
    data() {
      return {
        dialogVisible: false,
        formValidate: Object.assign({},obj),
        loading: false,
        rules:{
          name: [
            {  required: true, message: this.$t('user.grade.form.validation.levelNameRequired'), trigger: 'blur' }
          ],
          grade: [
            {  required: true, message: this.$t('user.grade.form.validation.gradeRequired'), trigger: 'blur' },
            { type: 'number', message: this.$t('user.grade.form.validation.gradeNumber')}
          ],
          discount: [
            {  required: true, message: this.$t('user.grade.form.validation.discountRequired'), trigger: 'blur'},
          ],
          experience: [
            {  required: true, message: this.$t('user.grade.form.validation.experienceRequired'), trigger: 'blur'},
            { type: 'number', message: this.$t('user.grade.form.validation.experienceNumber')}
          ],
          icon: [
            {  required: true, message: this.$t('user.grade.form.validation.iconRequired'), trigger: 'change' }
          ],
          image: [
            {  required: true, message: this.$t('user.grade.form.validation.imageRequired'), trigger: 'change' }
          ],
        }
      }
    },
    methods:{
      // 点击商品图
      modalPicTap (tit, num) {
         const _this = this
        this.$modalUpload(function(img) {
          tit==='1'&& num === 'icon' ? _this.formValidate.icon = img[0].sattDir : _this.formValidate.image = img[0].sattDir
          this.$set(_this.user,'icon', _this.formValidate.icon);
          this.$set(_this.user,'isShow', false);
        },tit , 'user')
      },
      info(id) {
        this.loading = true
        levelInfoApi({id: id}).then(res => {
          this.formValidate = res
          this.loading = false
        }).catch(() => {
          this.loading = false
        })
      },
      handleClose() {
         this.$nextTick(() => {
          this.$refs.user.resetFields();
        })
        this.dialogVisible = false;
        // this.user = Object.assign({}, '')
      },
      submitForm:Debounce(function(formName) {
        this.$refs.user.validate((valid) => {
          if (valid) {
            this.loading = true
            let data = {
              discount:this.user.discount,
              experience:this.user.experience,
              grade:this.user.grade,
              icon:this.user.icon,
              id:this.user.id,
              isShow:this.user.isShow,
              name:this.user.name
            };
            this.user.id ? levelUpdateApi(this.user.id, data).then(res => {
              this.$message.success(this.$t('user.grade.form.editSuccess'))
              this.loading = false
              this.handleClose()
              this.formValidate = Object.assign({},obj)
              this.$parent.getList()
            }).catch(() => {
              this.loading = false
            }): levelSaveApi(this.user).then(res => {
              this.$message.success(this.$t('user.grade.form.addSuccess'))
              this.loading = false
              this.handleClose()
              this.formValidate = Object.assign({},obj)
              this.$parent.getList()
            }).catch(() => {
              this.loading = false
              this.formValidate = Object.assign({},obj)
            })
          } else {
            return false;
          }
        });
      }),
      resetForm(formName) {
        
        // this[formName] = {};
         this.$nextTick(() => {
          this.$refs.user.resetFields();
        })
        this.dialogVisible = false;
      }
    }
  }
</script>

<style scoped>

</style>
