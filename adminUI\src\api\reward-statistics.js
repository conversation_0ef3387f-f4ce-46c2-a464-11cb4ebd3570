import request from '@/utils/request'

/**
 * 获取奖励统计概览
 * @param params 查询参数
 */
export function getRewardStatistics(params) {
  return request({
    url: '/api/admin/reward/statistics/overview',
    method: 'get',
    params
  })
}

/**
 * 获取奖励发放明细列表
 * @param params 查询参数
 */
export function getRewardDetails(params) {
  return request({
    url: '/api/admin/reward/statistics/details',
    method: 'get',
    params
  })
}

/**
 * 导出奖励发放明细
 * @param data 查询参数
 */
export function exportRewardDetails(data) {
  return request({
    url: '/api/admin/reward/statistics/export',
    method: 'post',
    data
  })
}
