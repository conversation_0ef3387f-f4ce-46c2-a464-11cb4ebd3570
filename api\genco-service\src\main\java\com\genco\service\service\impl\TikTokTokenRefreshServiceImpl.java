package com.genco.service.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.genco.common.constants.Constants;
import com.genco.common.constants.SysConfigConstants;
import com.genco.common.exception.CrmebException;
import com.genco.common.token.TikTokOauthToken;
import com.genco.service.service.SystemConfigService;
import com.genco.service.service.TikTokTokenRefreshService;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * TikTok Token刷新服务实现
 * 统一处理TikTok token的刷新逻辑
 *
 * <AUTHOR> Assistant
 * @date 2025-08-08
 */
@Service
public class TikTokTokenRefreshServiceImpl implements TikTokTokenRefreshService {

    private static final Logger log = LoggerFactory.getLogger(TikTokTokenRefreshServiceImpl.class);

    @Autowired
    private SystemConfigService systemConfigService;

    @Override
    public TikTokOauthToken refreshAccessToken(String appKey, String appSecret, String refreshToken) throws Exception {
        if (StrUtil.isBlank(appKey)) {
            throw new CrmebException("TikTok appKey未设置");
        }
        if (StrUtil.isBlank(appSecret)) {
            throw new CrmebException("TikTok appSecret未设置");
        }
        if (StrUtil.isBlank(refreshToken)) {
            throw new CrmebException("TikTok refreshToken未设置");
        }

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 使用GET请求，参数通过URL传递
            String refreshUrl = Constants.TIKTOK_ACCESS_TOKEN_REFRESH_URL +
                "?app_key=" + java.net.URLEncoder.encode(appKey, "UTF-8") +
                "&app_secret=" + java.net.URLEncoder.encode(appSecret, "UTF-8") +
                "&refresh_token=" + java.net.URLEncoder.encode(refreshToken, "UTF-8") +
                "&grant_type=refresh_token";

            HttpGet httpGet = new HttpGet(refreshUrl);
            httpGet.setHeader("Cache-Control", "no-cache");

            log.info("TikTok token刷新请求 - URL: {}", Constants.TIKTOK_ACCESS_TOKEN_REFRESH_URL);

            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                int statusCode = response.getStatusLine().getStatusCode();
                String responseBody = EntityUtils.toString(response.getEntity());

                log.info("TikTok token刷新响应 - 状态码: {}", statusCode);

                if (statusCode != 200) {
                    throw new CrmebException("TikTok token刷新失败，HTTP状态码: " + statusCode + ", 响应: " + responseBody);
                }

                if (ObjectUtil.isNull(responseBody)) {
                    throw new CrmebException("平台接口异常，没任何数据返回！");
                }

                return parseTokenResponse(responseBody);
            }
        }
    }

    @Override
    public boolean refreshAndUpdateToken() {
        try {
            String appKey = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_APP_KEY);
            String appSecret = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_APP_SECRET);
            String refreshToken = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_TOKEN_REFRESH_CODE);

            TikTokOauthToken tikTokOauthToken = refreshAccessToken(appKey, appSecret, refreshToken);

            if (tikTokOauthToken != null && StringUtils.isNoneBlank(tikTokOauthToken.getAccessToken())) {
                // 更新系统配置
                systemConfigService.updateOrSaveValueByName(SysConfigConstants.TIKTOK_TOKEN_REFRESH_CODE, tikTokOauthToken.getRefreshToken());
                systemConfigService.updateOrSaveValueByName(SysConfigConstants.TIKTOK_ACCESS_TOKEN, tikTokOauthToken.getAccessToken());
                systemConfigService.updateOrSaveValueByName(SysConfigConstants.TIKTOK_TOKEN_EXPIRE_IN, tikTokOauthToken.getExpiresIn().toString());

                log.info("TikTok access_token刷新成功，新的过期时间: {}", tikTokOauthToken.getExpiresIn());
                return true;
            } else {
                log.error("TikTok token刷新失败，返回内容: {}", tikTokOauthToken);
                return false;
            }
        } catch (Exception e) {
            log.error("TikTok token刷新异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 解析TikTok API响应
     *
     * @param responseBody 响应体
     * @return 解析后的token对象
     * @throws CrmebException 解析失败时抛出异常
     */
    private TikTokOauthToken parseTokenResponse(String responseBody) throws CrmebException {
        // 解析TikTok API响应结构
        JSONObject responseJson = JSONObject.parseObject(responseBody);
        Integer code = responseJson.getInteger("code");
        String message = responseJson.getString("message");

        if (code == null || code != 0) {
            throw new CrmebException("TikTok API返回错误: code=" + code + ", message=" + message);
        }

        JSONObject dataJson = responseJson.getJSONObject("data");
        if (dataJson == null) {
            throw new CrmebException("TikTok API响应中缺少data字段");
        }

        // 从data字段中解析token信息
        TikTokOauthToken tikTokOauthToken = new TikTokOauthToken();
        tikTokOauthToken.setAccessToken(dataJson.getString("access_token"));
        tikTokOauthToken.setRefreshToken(dataJson.getString("refresh_token"));

        // 正确处理时间戳类型转换
        Long accessTokenExpireIn = dataJson.getLong("access_token_expire_in");
        if (accessTokenExpireIn != null) {
            tikTokOauthToken.setExpiresIn(accessTokenExpireIn.intValue());
        }

        tikTokOauthToken.setOpenId(dataJson.getString("open_id"));
        tikTokOauthToken.setSellerName(dataJson.getString("seller_name"));
        tikTokOauthToken.setUserType(dataJson.getInteger("user_type"));

        // 设置refresh_token过期时间
        Long refreshTokenExpireIn = dataJson.getLong("refresh_token_expire_in");
        if (refreshTokenExpireIn != null) {
            tikTokOauthToken.setRefreshExpiresIn(refreshTokenExpireIn.intValue());
        }

        log.info("解析token成功 - access_token: {}..., expires_in: {}",
            tikTokOauthToken.getAccessToken() != null ? tikTokOauthToken.getAccessToken().substring(0, 20) : "null",
            tikTokOauthToken.getExpiresIn());

        return tikTokOauthToken;
    }
}
