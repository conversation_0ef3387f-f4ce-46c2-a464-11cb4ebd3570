<template>
  <div class="divBox relative">
    <!-- 搜索表单 -->
    <el-card class="box-card">
      <el-form :model="searchForm" :rules="searchRules" ref="searchFormRef" inline size="small">

        <el-form-item :label="$t('affiliateProducts.titleKeyword')">
          <el-input
            v-model="searchForm.titleKeyword"
            :placeholder="$t('affiliateProducts.titleKeywordPlaceholder')"
            style="width: 300px;"
            clearable
            maxlength="255"
            show-word-limit
          ></el-input>
        </el-form-item>

        <el-form-item :label="$t('affiliateProducts.priceRange')">
          <el-input
            v-model="searchForm.priceMin"
            :placeholder="$t('affiliateProducts.minPrice')"
            style="width: 160px;"
          ></el-input>
          <span style="margin: 0 8px;">-</span>
          <el-input
            v-model="searchForm.priceMax"
            :placeholder="$t('affiliateProducts.maxPrice')"
            style="width: 160px;"
          ></el-input>
        </el-form-item>

        <el-form-item prop="commissionRange">
          <span slot="label">
            {{ $t('affiliateProducts.commissionRange') }}
            <el-tooltip
              :content="$t('affiliateProducts.commissionRangeHint')"
              placement="top"
              effect="dark"
            >
              <i class="el-icon-info" style="color: #409EFF; margin-left: 4px; cursor: pointer;"></i>
            </el-tooltip>
          </span>
          <el-input
            v-model="searchForm.commissionMin"
            :placeholder="$t('affiliateProducts.minCommission')"
            style="width: 160px;"
            @blur="validateCommissionRate('commissionMin')"
            @input="clearCommissionError('commissionMin')"
          ></el-input>
          <span style="margin: 0 8px;">-</span>
          <el-input
            v-model="searchForm.commissionMax"
            :placeholder="$t('affiliateProducts.maxCommission')"
            style="width: 160px;"
            @blur="validateCommissionRate('commissionMax')"
            @input="clearCommissionError('commissionMax')"
          ></el-input>
          <div v-if="commissionErrors.commissionMin" class="commission-error">
            {{ commissionErrors.commissionMin }}
          </div>
          <div v-if="commissionErrors.commissionMax" class="commission-error">
            {{ commissionErrors.commissionMax }}
          </div>
        </el-form-item>

        <el-form-item :label="$t('affiliateProducts.soldQuantityRange')">
          <el-input
            v-model="searchForm.soldQuantityMin"
            :placeholder="$t('affiliateProducts.minSoldQuantity')"
            style="width: 120px;"
          ></el-input>
          <span style="margin: 0 8px;">-</span>
          <el-input
            v-model="searchForm.soldQuantityMax"
            :placeholder="$t('affiliateProducts.maxSoldQuantity')"
            style="width: 120px;"
          ></el-input>
        </el-form-item>

        <el-form-item :label="$t('affiliateProducts.shopRatingRange')">
          <el-input
            v-model="searchForm.shopRatingMin"
            :placeholder="$t('affiliateProducts.minShopRating')"
            style="width: 120px;"
          ></el-input>
          <span style="margin: 0 8px;">-</span>
          <el-input
            v-model="searchForm.shopRatingMax"
            :placeholder="$t('affiliateProducts.maxShopRating')"
            style="width: 120px;"
          ></el-input>
        </el-form-item>

<!--        <el-form-item :label="$t('affiliateProducts.sort')">-->
<!--          <el-select v-model="searchForm.sortType" style="width: 160px;">-->
<!--            <el-option :label="$t('affiliateProducts.sortRecommended')" value="RECOMMENDED"></el-option>-->
<!--            <el-option :label="$t('affiliateProducts.sortBestSellers')" value="BEST_SELLERS"></el-option>-->
<!--            <el-option :label="$t('affiliateProducts.sortLowPrice')" value="LOW_PRICE"></el-option>-->
<!--            <el-option :label="$t('affiliateProducts.sortHighPrice')" value="HIGH_PRICE"></el-option>-->
<!--            <el-option :label="$t('affiliateProducts.sortNewlyReleased')" value="NEWLY_RELEASED"></el-option>-->
<!--            <el-option :label="$t('affiliateProducts.sortHighCommission')" value="HIGH_COMMISSIOM_RATE"></el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
<!--        &lt;!&ndash; V2新增筛选项 &ndash;&gt;-->
<!--        <el-form-item :label="$t('affiliateProducts.categoryIds')">-->
<!--          <el-input-->
<!--            v-model="searchForm.categoryIds"-->
<!--            :placeholder="$t('affiliateProducts.categoryIdsPlaceholder')"-->
<!--            style="width: 300px;"-->
<!--            clearable-->
<!--          ></el-input>-->
<!--          <div style="font-size: 12px; color: #999; margin-top: 4px;">-->
<!--            {{ $t('affiliateProducts.categoryIdsHint') }}-->
<!--          </div>-->
<!--        </el-form-item>-->

<!--        <el-form-item :label="$t('affiliateProducts.poolIds')">-->
<!--          <el-input-->
<!--            v-model="searchForm.poolIds"-->
<!--            :placeholder="$t('affiliateProducts.poolIdsPlaceholder')"-->
<!--            style="width: 300px;"-->
<!--            clearable-->
<!--          ></el-input>-->
<!--          <div style="font-size: 12px; color: #999; margin-top: 4px;">-->
<!--            {{ $t('affiliateProducts.poolIdsHint') }}-->
<!--          </div>-->
<!--        </el-form-item>-->

<!--        <el-form-item :label="$t('affiliateProducts.productIds')">-->
<!--          <el-input-->
<!--            v-model="searchForm.productIds"-->
<!--            :placeholder="$t('affiliateProducts.productIdsPlaceholder')"-->
<!--            style="width: 300px;"-->
<!--            clearable-->
<!--          ></el-input>-->
<!--          <div style="font-size: 12px; color: #999; margin-top: 4px;">-->
<!--            {{ $t('affiliateProducts.productIdsHint') }}-->
<!--          </div>-->
<!--        </el-form-item>-->

      </el-form>

      <!-- 操作按钮区域 -->
      <div class="search-actions">
        <el-button size="small" type="primary" @click="handleSearch">{{ $t('affiliateProducts.query') }}</el-button>
        <el-button size="small" @click="handleReset">{{ $t('affiliateProducts.reset') }}</el-button>
      </div>


    </el-card>

    <!-- 产品列表 -->
    <el-card class="box-card" style="margin-top: 12px;">

      <!-- 批量操作按钮 -->
      <div v-if="hasSearched && tableData.length > 0" style="margin-bottom: 15px;">
        <el-button
          type="primary"
          size="small"
          @click="handleBatchImport"
          :disabled="selectedProducts.length === 0 || batchImporting"
        >
          {{ batchImporting ? $t('affiliateProducts.batchImporting') : `${$t('affiliateProducts.batchImport')} (${selectedProducts.length})` }}
        </el-button>
<!--        <el-button-->
<!--          type="danger"-->
<!--          size="small"-->
<!--          @click="handleBatchDelete"-->
<!--          :disabled="selectedProducts.length === 0 || batchDeleting"-->
<!--        >-->
<!--          {{ batchDeleting ? $t('affiliateProducts.batchDeleting') : `${$t('affiliateProducts.batchDelete')} (${selectedProducts.length})` }}-->
<!--        </el-button>-->
      </div>

      <el-table
        ref="productTable"
        v-loading="loading"
        :data="tableData"
        size="small"
        :header-cell-style="{ fontWeight: 'bold' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column type="index" :label="$t('affiliateProducts.serialNumber')" width="60"></el-table-column>

        <el-table-column :label="$t('affiliateProducts.productImage')" width="100">
          <template slot-scope="scope">
            <el-image
              :src="scope.row.mainImageUrl"
              fit="cover"
              style="width: 60px; height: 60px; border-radius: 4px;"
              :preview-src-list="[scope.row.mainImageUrl]"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.productTitle')" min-width="100">
          <template slot-scope="scope">
            <el-link
              :disabled="!scope.row.affiliateShareLink"
              :href="scope.row.affiliateShareLink"
              :type="scope.row.affiliateShareLink ? 'primary' : ''"
              :title="scope.row.affiliateShareLink ? $t('affiliateProducts.clickToOpenAffiliateLink') : $t('affiliateProducts.clickToOpenDetailLink')"
              target="_blank"
            >
              {{ scope.row.title }}
            </el-link>
            <!-- 显示链接类型提示 -->
            <el-tag
              v-if="scope.row.affiliateShareLink"
              size="mini"
              type="success"
              style="margin-left: 8px;"
            >
              {{ $t('affiliateProducts.affiliateLink') }}
            </el-tag>
            <el-tag
              v-else
              size="mini"
              type="info"
              style="margin-left: 8px;"
            >
              {{ $t('affiliateProducts.noLink') }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.brandName')" min-width="60">
          <template slot-scope="scope">
            {{ scope.row.brandName || '-' }}
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.shop')" min-width="60">
          <template slot-scope="scope">
            {{ (scope.row.shop && scope.row.shop.name) ? scope.row.shop.name : '-' }}
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.shopRating')" min-width="60">
          <template slot-scope="scope">
            {{ (scope.row.shop && scope.row.shop.rating !== null && scope.row.shop.rating !== undefined) ? scope.row.shop.rating.toFixed(1) : '-' }}
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.floorPrice')" min-width="60">
          <template slot-scope="scope">
            {{ formatPriceV2(scope.row.price ? scope.row.price.floorPrice : null, scope.row.price ? scope.row.price.currencyName : null) }}
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.ceilingPrice')" min-width="60">
          <template slot-scope="scope">
            {{ formatPriceV2(scope.row.price ? scope.row.price.ceilingPrice : null, scope.row.price ? scope.row.price.currencyName : null) }}
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.commissionRate')" min-width="60">
          <template slot-scope="scope">
            {{ scope.row.commission ? scope.row.commission.rateStr : '-' }}
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.commissionAmount')" min-width="60">
          <template slot-scope="scope">
            {{ formatPriceV2(scope.row.commission ? scope.row.commission.amount : null, scope.row.price ? scope.row.price.currencyName : '') }}
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.unitsSold')" min-width="60">
          <template slot-scope="scope">
            {{ (scope.row.marketPerformance && scope.row.marketPerformance.historicalSoldQuantity !== null && scope.row.marketPerformance.historicalSoldQuantity !== undefined) ? scope.row.marketPerformance.historicalSoldQuantity : 0 }}
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.inventoryStatus')" min-width="60">
          <template slot-scope="scope">
            <el-tag :type="(scope.row.stock && scope.row.stock.quantity !== null && scope.row.stock.quantity !== undefined && scope.row.stock.quantity > 0) ? 'success' : 'danger'" size="mini">
              {{ (scope.row.stock && scope.row.stock.quantity !== null && scope.row.stock.quantity !== undefined && scope.row.stock.quantity > 0) ? $t('affiliateProducts.hasInventory') : $t('affiliateProducts.noInventory') }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.reviewScore')" width="120">
          <template slot-scope="scope">
            {{ (scope.row.review && scope.row.review.overallScore !== null && scope.row.review.overallScore !== undefined) ? scope.row.review.overallScore : '-' }}
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.reviewCount')" width="120">
          <template slot-scope="scope">
            {{ (scope.row.review && scope.row.review.count !== null && scope.row.review.count !== undefined) ? scope.row.review.count : 0 }}
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.action')" min-width="60" fixed="right">
          <template slot-scope="scope">
            <el-button
              :type="scope.row.isImported ? 'success' : 'primary'"
              size="mini"
              @click="handleImportProduct(scope.row)"
              :disabled="scope.row.importing || scope.row.isImported"
            >
              {{ scope.row.importing ? $t('affiliateProducts.importing') : (scope.row.isImported ? $t('affiliateProducts.imported') : $t('affiliateProducts.import')) }}
            </el-button>
<!--            <el-button-->
<!--              type="danger"-->
<!--              size="mini"-->
<!--              @click="handleDeleteProduct(scope.row)"-->
<!--              :disabled="scope.row.deleting"-->
<!--            >-->
<!--              {{ scope.row.deleting ? $t('affiliateProducts.deleting') : $t('affiliateProducts.delete') }}-->
<!--            </el-button>-->
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div v-if="hasSearched" class="pagination-container" style="margin-top: 20px; text-align: center;">
        <el-button
          :disabled="!hasPrevPage"
          @click="handlePrevPage"
          size="small"
        >{{ $t('affiliateProducts.prevPage') }}</el-button>
        <el-button
          :disabled="!hasNextPage"
          @click="handleNextPage"
          size="small"
        >{{ $t('affiliateProducts.nextPage') }}</el-button>
        <span style="margin-left: 20px;">
          {{ $t('affiliateProducts.pageSize') }}
          <el-select v-model="searchForm.pageSize" size="mini" style="width: 80px;" @change="handleSearch">
            <el-option label="5" :value="5"></el-option>
            <el-option label="10" :value="10"></el-option>
            <el-option label="20" :value="20"></el-option>
            <el-option label="30" :value="30"></el-option>
            <el-option label="50" :value="50"></el-option>
          </el-select>
        </span>
        <span v-if="totalCount > 0" style="margin-left: 20px;">
          {{ $t('affiliateProducts.totalCount', { count: totalCount }) }}
        </span>
      </div>
    </el-card>

    <!-- 导入确认对话框 -->
    <el-dialog
      :title="currentImportProduct ? $t('affiliateProducts.importSingle') : $t('affiliateProducts.importBatch')"
      :visible.sync="importDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <div v-if="currentImportProduct">
        <p><strong>{{ $t('affiliateProducts.productTitle') }}：</strong>{{ currentImportProduct.title }}</p>
        <p><strong>{{ $t('product.productId') }}：</strong>{{ currentImportProduct.id }}</p>
      </div>
      <div v-else>
        <p><strong>{{ $t('affiliateProducts.selectedCount') }}</strong>{{ selectedProducts.length }}</p>
      </div>
      <div style="margin: 20px 0; padding: 15px; background-color: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 4px;">
        <i class="el-icon-info" style="color: #409eff; margin-right: 8px;"></i>
        <span style="color: #409eff;">{{ $t('affiliateProducts.brandAutoDetect') }}</span>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">{{ $t('affiliateProducts.cancel') }}</el-button>
        <el-button type="primary" @click="confirmImport">{{ $t('affiliateProducts.confirmImport') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { searchAffiliateProductsV2 } from '@/api/tiktok-v2'
import { importAffiliateProductsV2, deleteAffiliateProductsV2 } from '@/api/tiktok-v2'

export default {
  name: 'AffiliateProducts',
  data() {
    return {
      loading: false,
      tableData: [],
      searchForm: {
        titleKeyword: '', // 产品标题关键词
        priceMin: '',
        priceMax: '',
        commissionMin: '',
        commissionMax: '',
        shopRatingMin: '', // 商店评分最小值
        shopRatingMax: '', // 商店评分最大值
        soldQuantityMin: '', // 销量最小值
        soldQuantityMax: '', // 销量最大值
        sortType: 'RECOMMENDED', // 默认推荐排序
        pageSize: 10,
        // V2新增字段
        categoryIds: '', // 分类ID列表，逗号分隔
        poolIds: '', // 商品池ID列表，逗号分隔
        productIds: '' // 商品ID列表，逗号分隔（精确查询）
      },
      searchRules: {
        // 移除关键词必填验证
      },
      currentPageToken: '',
      nextPageToken: '',
      prevPageTokens: [], // 存储前面页面的token，用于返回上一页
      totalCount: 0,
      hasNextPage: false,
      hasPrevPage: false,
      hasSearched: false, // 标记是否已经搜索过
      selectedProducts: [], // 选中的商品列表
      batchImporting: false, // 批量入库状态
      batchDeleting: false, // 批量删除状态
      importDialogVisible: false, // 入库确认对话框
      currentImportProduct: null, // 当前导入的商品（单个导入时使用）
      commissionErrors: { // 佣金率验证错误信息
        commissionMin: '',
        commissionMax: ''
      },


    }
  },
  mounted() {
    // 页面加载时不自动搜索，等待用户点击查询按钮
  },
  methods: {
    // 佣金率验证
    validateCommissionRate(field) {
      const value = this.searchForm[field]
      if (value === '' || value === null || value === undefined) {
        this.commissionErrors[field] = ''
        return true
      }

      const numValue = parseFloat(value)
      if (isNaN(numValue)) {
        this.commissionErrors[field] = this.$t('affiliateProducts.commissionInvalidNumber')
        return false
      }

      if (numValue < 100 || numValue > 8000) {
        this.commissionErrors[field] = this.$t('affiliateProducts.commissionRangeError')
        return false
      }

      this.commissionErrors[field] = ''
      return true
    },

    // 清除佣金率错误信息
    clearCommissionError(field) {
      this.commissionErrors[field] = ''
    },





    // 搜索
    handleSearch() {
      // 验证佣金率字段
      const isCommissionMinValid = this.validateCommissionRate('commissionMin')
      const isCommissionMaxValid = this.validateCommissionRate('commissionMax')

      if (!isCommissionMinValid || !isCommissionMaxValid) {
        this.$message.error(this.$t('affiliateProducts.pleaseFixErrors'))
        return
      }

      this.currentPageToken = ''
      this.nextPageToken = ''
      this.prevPageTokens = []
      this.hasPrevPage = false
      this.hasSearched = true
      this.loadData()
    },

    // 重置
    handleReset() {
      this.searchForm = {
        titleKeyword: '',
        priceMin: '',
        priceMax: '',
        commissionMin: '',
        commissionMax: '',
        shopRatingMin: '',
        shopRatingMax: '',
        soldQuantityMin: '',
        soldQuantityMax: '',
        sortType: 'RECOMMENDED',
        pageSize: 10,
        // V2新增字段
        categoryIds: '',
        poolIds: '',
        productIds: ''
      }
      // 清除错误信息
      this.commissionErrors = {
        commissionMin: '',
        commissionMax: ''
      }
      // 清空表格数据
      this.tableData = []
      this.hasSearched = false
      this.totalCount = 0
      this.hasNextPage = false
      this.hasPrevPage = false
      this.currentPageToken = ''
      this.nextPageToken = ''
      this.prevPageTokens = []
    },

    // 刷新
    handleRefresh() {
      if (this.hasSearched) {
        this.loadData()
      } else {
        this.$message.warning(this.$t('affiliateProducts.searchFirst'))
      }
    },

    // 下一页
    handleNextPage() {
      if (this.hasNextPage && this.nextPageToken) {
        this.prevPageTokens.push(this.currentPageToken)
        this.currentPageToken = this.nextPageToken
        this.hasPrevPage = true
        this.loadData()
      }
    },

    // 上一页
    handlePrevPage() {
      if (this.hasPrevPage && this.prevPageTokens.length > 0) {
        this.currentPageToken = this.prevPageTokens.pop()
        if (this.prevPageTokens.length === 0) {
          this.hasPrevPage = false
        }
        this.loadData()
      }
    },

    // 加载数据
    loadData() {
      this.loading = true

      // 构建请求参数 - 根据 AffiliateProductSearchRequestV2 结构
      const params = {
        // 基础分页和排序参数
        pageSize: this.searchForm.pageSize,
        pageToken: this.currentPageToken,
        sortType: this.searchForm.sortType,

        // 搜索过滤参数
        titleKeyword: this.searchForm.titleKeyword || null,
        priceRangeGe: this.searchForm.priceMin || null,
        priceRangeLe: this.searchForm.priceMax || null,
        commissionRateGe: this.searchForm.commissionMin ? parseInt(parseFloat(this.searchForm.commissionMin) * 100) : null, // 转换为基点（整数）
        commissionRateLe: this.searchForm.commissionMax ? parseInt(parseFloat(this.searchForm.commissionMax) * 100) : null, // 转换为基点（整数）
        shopRatingGe: this.searchForm.shopRatingMin ? parseInt(parseFloat(this.searchForm.shopRatingMin) * 10) : null, // 转换为基点（整数）
        shopRatingLe: this.searchForm.shopRatingMax ? parseInt(parseFloat(this.searchForm.shopRatingMax) * 10) : null, // 转换为基点（整数）
        soldQuantityGe: this.searchForm.soldQuantityMin ? parseInt(this.searchForm.soldQuantityMin) : null,
        soldQuantityLe: this.searchForm.soldQuantityMax ? parseInt(this.searchForm.soldQuantityMax) : null,

        // V2新增字段处理
        categoryIds: this.searchForm.categoryIds ? this.searchForm.categoryIds.split(',').map(id => id.trim()).filter(id => id) : null,
        poolIds: this.searchForm.poolIds ? this.searchForm.poolIds.split(',').map(id => id.trim()).filter(id => id) : null,
        productIds: this.searchForm.productIds ? this.searchForm.productIds.split(',').map(id => id.trim()).filter(id => id) : null,
      }

      // 移除空值参数
      Object.keys(params).forEach(key => {
        if (params[key] === null || params[key] === undefined || params[key] === '') {
          delete params[key]
        }
      })

      searchAffiliateProductsV2(params)
        .then(res => {
          this.tableData = res.products || []
          this.nextPageToken = res.nextPageToken || ''
          this.totalCount = res.totalCount || 0
          this.hasNextPage = !!this.nextPageToken

          // 添加数据处理，确保每个产品都有必要的字段
          this.tableData = this.tableData.map(product => {
            return {
              ...product,
              // 确保嵌套对象存在
              price: product.price || {},
              commission: product.commission || {},
              shop: product.shop || {},
              stock: product.stock || {},
              review: product.review || {},
              marketPerformance: product.marketPerformance || {}
            }
          })

          if (this.tableData.length === 0) {
            this.$message.info(this.$t('affiliateProducts.noResults'))
          }
        })
        .catch(() => {
          this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 格式化价格（V2版本）- 支持字符串价格和货币
    formatPriceV2(priceStr, currency = '') {
      if (!priceStr || priceStr === '' || priceStr === null || priceStr === undefined) return '-'
      // 处理数字类型的价格
      if (typeof priceStr === 'number') {
        priceStr = priceStr.toString()
      }
      return `${priceStr} ${currency || ''}`
    },

    // 格式化佣金率（V2版本）- rate字段是基点，例如500表示5%
    formatCommissionRateV2(rate) {
      if (!rate || rate === 0 || rate === null || rate === undefined) return '0'
      // 确保rate是数字类型
      const numRate = typeof rate === 'string' ? parseFloat(rate) : rate
      if (isNaN(numRate)) return '0'
      return (numRate / 100).toFixed(2)
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedProducts = selection
    },

    // 单个商品导入
    handleImportProduct(product) {
      // 设置当前操作的商品
      this.currentImportProduct = product
      this.importDialogVisible = true
    },

    // 单个商品删除
    handleDeleteProduct(product) {
      this.$confirm(this.$t('affiliateProducts.deleteConfirm'), this.$t('common.deleteConfirm'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      })
      .then(() => {
        product.deleting = true

        deleteAffiliateProductsV2([product.id])
        .then(() => {
          this.$message.success(this.$t('affiliateProducts.deleteSuccess'))
        })
        .catch(() => {
          // 错误消息已在响应拦截器中处理
        })
        .finally(() => {
          product.deleting = false
        })
      })
      .catch(() => {
        // 用户取消删除，不需要处理
      })
    },

    // 批量导入
    handleBatchImport() {
      if (this.selectedProducts.length === 0) {
        this.$message.warning(this.$t('affiliateProducts.selectFirst'))
        return
      }

      // 设置批量导入模式
      this.currentImportProduct = null
      this.importDialogVisible = true
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedProducts.length === 0) {
        this.$message.warning(this.$t('affiliateProducts.selectDeleteFirst'))
        return
      }

      this.$confirm(this.$t('affiliateProducts.batchDeleteConfirm', { count: this.selectedProducts.length }), this.$t('affiliateProducts.batchDelete'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      })
      .then(() => {
        this.batchDeleting = true

        const productIds = this.selectedProducts.map(p => p.id)
        deleteAffiliateProductsV2(productIds)
        .then(() => {
          this.$message.success(this.$t('affiliateProducts.batchDeleteSuccess'))
          // 清空选择
          this.$refs.productTable.clearSelection()
        })
        .catch(() => {
          // 错误消息已在响应拦截器中处理
        })
        .finally(() => {
          this.batchDeleting = false
        })
      })
      .catch(() => {
        // 用户取消删除，不需要处理
      })
    },



    // 确认导入
    confirmImport() {
      if (this.currentImportProduct) {
        // 单个商品导入
        this.currentImportProduct.importing = true
        this.importDialogVisible = false

        importAffiliateProductsV2({
          productIds: [this.currentImportProduct.id],
          operationUser: this.$store.getters.name || 'admin'
        })
        .then(data => {
          // 根据导入结果显示不同的消息
          if (data.failedCount === 0) {
            if (data.skippedCount > 0) {
              this.$message.warning(this.$t('affiliateProducts.importExists'))
            } else {
              this.$message.success(this.$t('affiliateProducts.importSuccess'))
            }
          } else {
            this.$message.error(this.$t('affiliateProducts.importFailed', {
              reason: data.failedProducts && data.failedProducts.length > 0 ? data.failedProducts[0].errorMessage : this.$t('common.unknownError')
            }))
          }
        })
        .catch(() => {
          // 错误消息已在响应拦截器中处理
        })
        .finally(() => {
          this.currentImportProduct.importing = false
        })
      } else {
        // 批量导入
        this.batchImporting = true
        this.importDialogVisible = false

        const productIds = this.selectedProducts.map(p => p.id)
        importAffiliateProductsV2({
          productIds,
          operationUser: this.$store.getters.name || 'admin'
        })
        .then(data => {
          // 根据导入结果显示不同的消息
          if (data.failedCount === 0) {
            if (data.skippedCount > 0 && data.successCount === 0) {
              this.$message.warning(this.$t('affiliateProducts.batchImportExists'))
            } else if (data.skippedCount > 0) {
              this.$message.success(this.$t('affiliateProducts.batchImportMixed', {
                success: data.successCount,
                skipped: data.skippedCount
              }))
            } else {
              this.$message.success(this.$t('affiliateProducts.batchImportSuccess', { count: data.successCount }))
            }
          } else if (data.successCount > 0) {
            // 部分成功
            this.$message.warning(this.$t('affiliateProducts.batchImportPartial', {
              success: data.successCount,
              failed: data.failedCount,
              skipped: data.skippedCount
            }))
          } else {
            // 全部失败
            this.$message.error(this.$t('affiliateProducts.batchImportFailed', {
              failed: data.failedCount,
              skipped: data.skippedCount
            }))
          }

          // 清空选择
          this.$refs.productTable.clearSelection()
        })
        .catch(() => {
          // 错误消息已在响应拦截器中处理
        })
        .finally(() => {
          this.batchImporting = false
        })
      }
    },
  }
}
</script>

<style scoped>
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.empty-tip {
  padding: 40px 0;
  text-align: center;
}

.commission-error {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.2;
}

/* 关键词展示区域样式 */
.keywords-display-section {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}



/* 搜索操作按钮区域 */
.search-actions {
  display: flex;
  gap: 8px;
}
</style>
