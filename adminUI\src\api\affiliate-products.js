import request from '@/utils/request'

/**
 * 批量导入联盟商品
 * @param {Object} data 导入请求数据
 * @param {Array} data.productIds 商品ID列表
 * @param {Number} data.brandId 品牌ID
 * @param {String} data.operationUser 操作用户
 */
export function importAffiliateProducts(data) {
  return request({
    url: '/admin/affiliate/products/import',
    method: 'post',
    data
  })
}

/**
 * 批量删除联盟商品
 * @param {Array} productIds 商品ID列表
 */
export function deleteAffiliateProducts(productIds) {
  return request({
    url: '/admin/affiliate/products/delete',
    method: 'post',
    data: productIds
  })
}

/**
 * 查询商品操作历史
 * @param {Object} params 查询参数
 * @param {String} params.productId 商品ID
 * @param {Number} params.status 状态
 * @param {String} params.operationType 操作类型
 * @param {Number} params.page 页码
 * @param {Number} params.limit 每页数量
 */
export function getAffiliateProductHistory(params) {
  return request({
    url: '/admin/affiliate/products/history',
    method: 'get',
    params
  })
}

/**
 * 检查商品是否已存在
 * @param {Array} productIds 商品ID列表
 */
export function checkProductsExist(productIds) {
  return request({
    url: '/admin/affiliate/products/check-existing',
    method: 'post',
    data: { productIds }
  })
}
