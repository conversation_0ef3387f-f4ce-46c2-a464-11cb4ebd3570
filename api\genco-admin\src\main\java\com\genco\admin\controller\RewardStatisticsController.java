package com.genco.admin.controller;

import com.genco.common.request.RewardStatisticsRequest;
import com.genco.common.response.CommonResult;
import com.genco.common.response.RewardStatisticsResponse;
import com.genco.service.service.RewardStatisticsService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 奖励统计管理控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/reward/statistics")
@Api(tags = "奖励统计管理")
public class RewardStatisticsController {

    @Autowired
    private RewardStatisticsService rewardStatisticsService;

    /**
     * 获取奖励统计概览
     */
    @PreAuthorize("hasAuthority('admin:reward:statistics:overview')")
    @ApiOperation(value = "获取奖励统计概览")
    @RequestMapping(value = "/overview", method = RequestMethod.GET)
    public CommonResult<RewardStatisticsResponse> getRewardOverview(@Validated RewardStatisticsRequest request) {
        RewardStatisticsResponse statistics = rewardStatisticsService.getRewardStatistics(request);
        return CommonResult.success(statistics);
    }

    /**
     * 获取奖励发放明细列表
     */
    @PreAuthorize("hasAuthority('admin:reward:statistics:detail')")
    @ApiOperation(value = "获取奖励发放明细列表")
    @RequestMapping(value = "/details", method = RequestMethod.GET)
    public CommonResult<PageInfo<RewardStatisticsResponse.RewardDetailItem>> getRewardDetails(@Validated RewardStatisticsRequest request) {
        PageInfo<RewardStatisticsResponse.RewardDetailItem> details = rewardStatisticsService.getRewardDetailList(request);
        return CommonResult.success(details);
    }

    /**
     * 导出奖励发放明细
     */
    @PreAuthorize("hasAuthority('admin:reward:statistics:export')")
    @ApiOperation(value = "导出奖励发放明细")
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    public CommonResult<String> exportRewardDetails(@RequestBody @Validated RewardStatisticsRequest request) {
        String filePath = rewardStatisticsService.exportRewardDetails(request);
        return CommonResult.success(filePath, "导出成功");
    }
}
