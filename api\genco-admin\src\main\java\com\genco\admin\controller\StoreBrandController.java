package com.genco.admin.controller;

import com.genco.common.page.CommonPage;
import com.genco.common.request.PageParamRequest;
import com.genco.common.request.StoreBrandUpdateRequest;
import com.genco.common.response.BrandUpdateResult;
import com.genco.common.response.CommonResult;
import com.genco.common.response.StoreBrandResponse;
import com.genco.service.service.BrandService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 品牌管理控制器（admin端）
 */
@Slf4j
@RestController
@RequestMapping("api/admin/brand")
@Api(tags = "品牌管理")
public class StoreBrandController {

    @Autowired
    private BrandService brandService;

    /**
     * 品牌列表
     *
     * @param type             类型（-1：全部；0：待上架；1：已上架；2：已下架；）
     * @param pageParamRequest 分页参数
     * @return 品牌分页列表
     *
     * 品牌状态使用规范：
     * - 新增品牌：默认 "0"(待上架)
     * - 审核通过：从 "0" → "1"(已上架)
     * - 临时下架：从 "1" → "2"(已下架)
     * - 重新上架：从 "2" → "1"(已上架)
     * - 逻辑删除：任何状态 → "-1"(已删除)
     */
    @ApiOperation(value = "品牌列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<StoreBrandResponse>> getBrandList(@RequestParam(value = "type") Integer type,
                                                                     @RequestParam(value = "name") String name,
                                                                     @Validated PageParamRequest pageParamRequest) {
        return CommonResult.success(brandService.getList(type, name, pageParamRequest));
    }

    /**
     * 更新品牌信息
     *
     * @param request 品牌更新请求对象
     * @return 是否更新成功
     */
    @ApiOperation(value = "更新品牌信息")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<Boolean> updateBrand(@RequestBody @Validated StoreBrandUpdateRequest request) {
        return CommonResult.success(brandService.updateBrand(request));
    }

    /**
     * 批量更新品牌信息
     *
     * @param requests 品牌更新请求对象列表
     * @return 每个品牌的更新结果
     */
    @ApiOperation(value = "批量更新品牌信息")
    @RequestMapping(value = "/batchUpdate", method = RequestMethod.POST)
    public CommonResult<List<BrandUpdateResult>> batchUpdateBrand(@RequestBody @Validated List<StoreBrandUpdateRequest> requests) {
        return CommonResult.success(brandService.batchUpdateBrand(requests));
    }

    /**
     * 新增品牌信息
     *
     * @param request 品牌新增请求对象
     * @return 新增品牌ID
     */
    @ApiOperation(value = "新增品牌信息")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public CommonResult<Integer> addBrand(@RequestBody @Validated StoreBrandUpdateRequest request) {
        return CommonResult.success(brandService.addBrand(request));
    }

    /**
     * 根据ID查询品牌信息
     *
     * @param id 品牌ID
     * @return 品牌信息
     */
    @ApiOperation(value = "根据ID查询品牌信息")
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public CommonResult<StoreBrandResponse> getById(@RequestParam Integer id) {
        return CommonResult.success(brandService.getById(id));
    }

    /**
     * 删除品牌信息（逻辑删除）
     *
     * @param id 品牌ID
     * @return 是否删除成功
     */
    @ApiOperation(value = "删除品牌信息")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public CommonResult<Boolean> deleteBrand(@RequestParam @NotNull Integer id) {
        return CommonResult.success(brandService.deleteBrand(id));
    }
} 