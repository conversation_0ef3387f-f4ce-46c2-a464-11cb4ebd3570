<template>
  <div class="divBox relative">
    <el-card class="box-card">
      <el-tabs
        v-model="searchForm.status"
        @tab-click="onChangeType"
        class="mb20"
      >
        <el-tab-pane :label="$t('common.pendingReview')" name="0"></el-tab-pane>
        <el-tab-pane
          :label="$t('common.reviewedPassed')"
          name="2"
        ></el-tab-pane>
        <el-tab-pane
          :label="$t('common.reviewedRejected')"
          name="-1"
        ></el-tab-pane>
      </el-tabs>
      <div class="container mt-1">
        <el-radio-group
          v-model="searchForm.extractType"
          @change="handleExtractTypeChange"
          size="small"
          style="margin-bottom: 18px;"
        >
          <el-radio-button label="wallet" value="wallet">{{
            $t("operations.withdrawal.walletWithdrawal")
          }}</el-radio-button>
          <el-radio-button label="bank" value="bank">{{
            $t("operations.withdrawal.bankWithdrawal")
          }}</el-radio-button>
        </el-radio-group>
        <el-form v-model="searchForm" inline size="small">
          <el-form-item :label="$t('operations.withdrawal.applicant') + '：'">
            <el-input
              v-model="searchForm.keywords"
              size="small"
              :placeholder="$t('common.enter')"
            ></el-input>
          </el-form-item>
          <el-form-item
            :label="$t('operations.withdrawal.applicationTime') + '：'"
          >
            <el-date-picker
              v-model="timeList"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              size="small"
              type="daterange"
              placement="bottom-end"
              :start-placeholder="$t('common.startDate')"
              :end-placeholder="$t('common.endDate')"
              style="width: 250px;"
            />
          </el-form-item>
          <el-form-item
            :label="$t('operations.withdrawal.electronicWallet') + '：'"
            v-if="searchForm.extractType == 'wallet'"
          >
            <el-select
              v-model="searchForm.walletCode"
              :placeholder="$t('common.all')"
              clearable
            >
              <el-option
                v-for="item in walletList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            :label="$t('operations.withdrawal.bankName') + '：'"
            v-if="searchForm.extractType == 'bank'"
          >
            <el-select
              v-model="searchForm.bankName"
              :placeholder="$t('common.all')"
            >
              <el-option
                v-for="(item, index) in bankList"
                :key="index"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <el-button size="small" type="primary" class="mr10" @click="getList(1)">{{
        $t("common.query")
      }}</el-button>
      <el-button size="small" type="" class="mr10" @click="resetForm">{{
        $t("common.reset")
      }}</el-button>
    </el-card>
    <el-card class="box-card" style="margin-top: 12px;">
      <el-table
        v-loading="loading"
        :data="tableData"
        size="small"
        :header-cell-style="{ fontWeight: 'bold' }"
      >
        <el-table-column type="index" :label="$t('common.serialNumber')"  width="110">
        </el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.applicationId')"
          min-width="80"
          prop="uid"
        >
        </el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.applicantName')"
          min-width="80"
          prop="realName"
        >
        </el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.withdrawalAmount')"
          min-width="80"
          prop="extractPrice"
        >
        </el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.serviceFee')"
          min-width="80"
          prop="serviceFee"
        >
        </el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.actualAmount')"
          min-width="100"
          prop="actualAmount"
        >
        </el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.applicationTime')"
          min-width="80"
          prop="createTime"
        >
        </el-table-column>
        <el-table-column
          v-if="searchForm.extractType === 'wallet'"
          :label="$t('operations.withdrawal.walletCode')"
          min-width="80"
          prop="walletCode"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.extractType === 'wallet'">{{ scope.row.walletCode | filterEmpty }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="searchForm.extractType === 'wallet'"
          :label="$t('operations.withdrawal.walletAccount')"
          min-width="80"
          prop="walletAccount"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.extractType === 'wallet'">{{ scope.row.walletAccount | filterEmpty }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="searchForm.extractType === 'bank'"
          :label="$t('operations.withdrawal.bankName')"
          min-width="80"
          prop="bankName"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.extractType === 'bank'">{{ scope.row.bankName | filterEmpty }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="searchForm.extractType === 'bank'"
          :label="$t('operations.withdrawal.bankCardNumber')"
          min-width="80"
          prop="bankCode"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.extractType === 'bank'">{{ scope.row.bankCode | filterEmpty }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column
          :label="$t('operations.withdrawal.name')"
          min-width="80"
          prop="nickName"
        >
        </el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.phoneNumber')"
          min-width="80"
          prop="phone"
        >
        </el-table-column>
        <!-- 隐藏历史提现次数列 -->
        <!-- <el-table-column
          :label="$t('operations.withdrawal.withdrawalCount')"
          min-width="80"
        >
        </el-table-column> -->
        <!-- 新增审核状态列 -->
        <el-table-column
          :label="$t('operations.withdrawal.auditStatus')"
          min-width="100"
          prop="status"
        >
          <template slot-scope="scope">
            <el-tag
              :type="getStatusTagType(scope.row.status)"
              size="small"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <!-- 新增交易流水号列 -->
        <el-table-column
          :label="$t('operations.withdrawal.transactionNumber')"
          min-width="120"
          prop="transactionNumber"
          v-if="searchForm.status === '2'"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.transactionNumber | filterEmpty }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.auditResult')"
          min-width="80"
          v-if="searchForm.status !== '0'"
        >
        </el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.rejectReason')"
          min-width="120"
          prop="failMsg"
          v-if="searchForm.status === '1'"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.backMessage | filterEmpty }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('product.action')"
          min-width="80"
          v-if="searchForm.status === '0'"
        >
          <template slot-scope="scope">
            <!-- 只有待审核状态(status=0)的记录才显示操作按钮 -->
            <template v-if="scope.row.status === 0">
              <el-button
                size="small"
                type="text"
                @click="handleOpen(scope.row, 1)"
                >{{ $t("operations.withdrawal.approve") }}</el-button
              >
              <el-button
                size="small"
                type="text"
                @click="handleOpen(scope.row, -1)"
                >{{ $t("operations.withdrawal.reject") }}</el-button
              >
            </template>
            <!-- 已处理的记录不显示操作按钮 -->
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="mt20"
        @size-change="sizeChange"
        @current-change="pageChange"
        :current-page="searchForm.page"
        :page-sizes="[20, 40, 60, 100]"
        :page-size="searchForm.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="searchForm.total"
      >
      </el-pagination>
      <el-dialog
        append-to-body
        :visible.sync="dialogFormVisible"
        :title="getDialogTitle()"
        width="680px"
        @close="handleCancle"
      >
        <el-form
          ref="elForm"
          :model="artFrom"
          :rules="rules"
          label-width="120px"
        >
          <!-- 审核通过表单 -->
          <template v-if="artFrom.status === 1">
            <el-form-item
              :label="$t('operations.withdrawal.transactionNumber') + '：'"
              prop="transactionNumber"
            >
              <el-input
                v-model="artFrom.transactionNumber"
                size="small"
                :placeholder="$t('operations.withdrawal.enterTransactionNumber')"
                style="width: 300px;"
              ></el-input>
            </el-form-item>
            <el-form-item
              :label="$t('operations.withdrawal.transactionRemark') + '：'"
              prop="transactionRemark"
            >
              <el-input
                v-model="artFrom.transactionRemark"
                type="textarea"
                :rows="3"
                :placeholder="$t('operations.withdrawal.enterTransactionRemark')"
                style="width: 300px;"
              ></el-input>
            </el-form-item>
          </template>

          <!-- 审核拒绝表单 -->
          <template v-if="artFrom.status === -1">
            <el-form-item
              :label="$t('operations.withdrawal.rejectReason') + '：'"
              prop="backMessage"
            >
              <el-input
                v-model="artFrom.backMessage"
                type="textarea"
                :rows="3"
                :placeholder="$t('operations.withdrawal.enterRejectReason')"
                style="width: 300px;"
              ></el-input>
            </el-form-item>
            <el-form-item
              :label="$t('operations.withdrawal.transactionRemark') + '：'"
              prop="transactionRemark"
            >
              <el-input
                v-model="artFrom.transactionRemark"
                type="textarea"
                :rows="3"
                :placeholder="$t('operations.withdrawal.enterTransactionRemark')"
                style="width: 300px;"
              ></el-input>
            </el-form-item>
          </template>
        </el-form>

        <div slot="footer">
          <el-button type="primary" @click="handelConfirm">
            {{ $t("common.confirm") }}
          </el-button>
          <el-button @click="handleCancle">
            {{ $t("common.cancel") }}
          </el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import { applyListApi, extractBankApi, financeApplyApi } from "@/api/financial";
export default {
  name: "WithdrawalRequest",
  data() {
    return {
      loading: false,
      tableData: [],
      artFrom: {
        backMessage: "",
        transactionNumber: "",
        transactionRemark: "",
        status: null
      },
      searchForm: {
        tableFromType: "",
        keywords: "",
        dateLimit: "",
        bankName: "",
        walletCode: "",
        extractType: "wallet",
        status: 0,
        page: 1,
        limit: 20,
        total: 0
      },
      timeList: [],
      dialogFormVisible: false,
      walletList: [
        { label: "ShopeePay", value: "ShopeePay" },
        { label: "DANA", value: "DANA" },
        { label: "OVO", value: "OVO" },
        { label: "Gopay", value: "Gopay" }
      ],
      bankList: [],

      rules: {
        backMessage: [
          {
            required: true,
            message: this.$t('operations.withdrawal.rejectReasonRequired'),
            trigger: "blur"
          }
        ],
        transactionNumber: [
          {
            required: true,
            message: this.$t('operations.withdrawal.transactionNumberRequired'),
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    // 确保初始化时提现类型和状态正确设置
    this.searchForm.extractType = 'wallet';
    this.searchForm.status = '0';
  },
  mounted() {
    this.getList();
    this.getBankList();
  },
  methods: {
    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        0: 'warning',   // 待审核
        '-1': 'danger', // 已拒绝
        1: 'success',   // 已提现
        2: 'success'    // 已通过
      };
      return statusMap[status] || 'info';
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: this.$t('common.pendingReview'),
        '-1': this.$t('common.reviewedRejected'),
        1: this.$t('common.reviewedPassed'),
        2: this.$t('common.reviewedPassed')
      };
      return statusMap[status] || this.$t('common.unknown');
    },
    // 获取弹窗标题
    getDialogTitle() {
      if (this.artFrom.status === 1) {
        return this.$t('operations.withdrawal.approveReview');
      } else if (this.artFrom.status === -1) {
        return this.$t('operations.withdrawal.rejectReview');
      }
      return this.$t('operations.withdrawal.auditOperation');
    },
    // 获取银行列表
    getBankList() {
      extractBankApi()
        .then(res => {
          this.bankList = res;
        })
        .catch(() => {});
    },
    // 列表
    getList(num) {
      this.loading = true;
      this.searchForm.page = num ? num : this.searchForm.page;
      this.searchForm.dateLimit = this.timeList.length
        ? this.timeList.join(",")
        : "";

      // 创建请求参数，确保extractType参数正确传递
      const params = { ...this.searchForm };

      applyListApi(params)
        .then(res => {
          // 确保后端返回的数据是数组
          const list = Array.isArray(res.list) ? res.list : [];

          // 根据当前提现类型过滤数据
          this.tableData = list.filter(item => item.extractType === this.searchForm.extractType);

          // 更新总数为过滤后的数量
          this.searchForm.total = res.total ? Math.min(res.total, this.tableData.length) : this.tableData.length;

          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    resetForm() {
      // 保存当前的提现类型和状态
      const currentExtractType = this.searchForm.extractType;
      const currentStatus = this.searchForm.status;

      this.searchForm = {
        keywords: "",
        dateLimit: "",
        bankName: "",
        walletCode: "",
        extractType: currentExtractType, // 保持提现类型不变
        status: currentStatus, // 保持状态不变
        page: 1,
        limit: 20,
        total: 0
      };
      this.timeList = [];
      this.getList();
    },
    //切换页数
    pageChange(index) {
      this.searchForm.page = index;
      this.getList();
    },
    //切换显示条数
    sizeChange(index) {
      this.searchForm.limit = index;
      this.getList();
    },
    handleUpload() {},
    onChangeType() {
      // 切换状态标签时重置搜索条件并获取列表
      this.resetForm();
    },
    handleExtractTypeChange() {
      // 切换提现类型时，清除不相关的筛选条件
      if (this.searchForm.extractType === 'wallet') {
        this.searchForm.bankName = '';
      } else if (this.searchForm.extractType === 'bank') {
        this.searchForm.walletCode = '';
      }
      // 重新获取列表数据
      this.getList(1);
    },
    handleOpen(row, status) {
      this.artFrom.id = row.id;
      // 修复状态映射：前端1=审核通过对应后端1，前端-1=审核拒绝对应后端-1
      this.artFrom.status = status;
      // 清空表单数据
      this.artFrom.backMessage = "";
      this.artFrom.transactionNumber = "";
      this.artFrom.transactionRemark = "";
      this.dialogFormVisible = true;
    },
    handleCancle() {
      this.dialogFormVisible = false;
      this.artFrom = {
        backMessage: "",
        transactionNumber: "",
        transactionRemark: "",
        status: null
      };
      // 清除表单验证
      if (this.$refs.elForm) {
        this.$refs.elForm.clearValidate();
      }
    },
    handleApi(row, status) {
      if (row.id) {
        this.artFrom.id = row.id;
        this.artFrom.status = status;
      }

      // 构建提交数据
      const submitData = {
        id: this.artFrom.id,
        status: this.artFrom.status
      };

      // 根据审核状态添加相应字段
      if (this.artFrom.status === 1) {
        // 审核通过
        submitData.transactionNumber = this.artFrom.transactionNumber;
        submitData.transactionRemark = this.artFrom.transactionRemark;
      } else if (this.artFrom.status === -1) {
        // 审核拒绝
        submitData.backMessage = this.artFrom.backMessage;
        submitData.transactionRemark = this.artFrom.transactionRemark;
      }

      // 使用封装好的API函数
      financeApplyApi(submitData)
        .then(res => {
          console.log('审核API响应:', res);
          // 操作成功
          this.$message.success(this.$t("common.operationSuccess"));
          this.dialogFormVisible = false;
          this.getList(); // 刷新列表
        })
        .catch(error => {
          console.error('审核操作失败:', error);
          // 显示错误消息
          let errorMsg = this.$t("common.operationFailed");
          if (error && error.message) {
            errorMsg = error.message;
          }
          this.$message.error(errorMsg);
        });
    },
    handelConfirm() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return;

        // 根据状态进行不同的验证
        if (this.artFrom.status === 1) {
          // 审核通过：验证交易流水号
          if (!this.artFrom.transactionNumber || this.artFrom.transactionNumber.trim() === '') {
            this.$message.error(this.$t('operations.withdrawal.transactionNumberRequired'));
            return;
          }
        } else if (this.artFrom.status === -1) {
          // 审核拒绝：验证拒绝原因
          if (!this.artFrom.backMessage || this.artFrom.backMessage.trim() === '') {
            this.$message.error(this.$t('operations.withdrawal.rejectReasonRequired'));
            return;
          }
        }

        this.handleApi({}, this.artFrom.status);
      });
    }
  }
};
</script>
<style scoped lang="scss">
/**/
</style>
