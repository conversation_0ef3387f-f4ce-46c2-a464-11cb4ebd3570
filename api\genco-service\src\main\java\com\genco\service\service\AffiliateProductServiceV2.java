package com.genco.service.service;

import com.genco.common.request.AffiliateProductSearchRequestV2;
import com.genco.common.response.AffiliateProductResponseV2;
import tiktokshop.open.sdk_java.invoke.ApiException;

/**
 * 联盟选品服务接口 V2
 */
public interface AffiliateProductServiceV2 {

    /**
     * 搜索联盟产品 V2
     *
     * @param request 搜索请求参数
     * @return 搜索结果
     */
    AffiliateProductResponseV2 searchProducts(AffiliateProductSearchRequestV2 request) throws ApiException;
}