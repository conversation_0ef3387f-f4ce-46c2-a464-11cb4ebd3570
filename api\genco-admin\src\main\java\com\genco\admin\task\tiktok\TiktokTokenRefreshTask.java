package com.genco.admin.task.tiktok;

import com.genco.common.utils.DateUtil;
import com.genco.service.service.SystemConfigService;
import com.genco.service.service.TikTokTokenRefreshService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;



/**
 * 定时任务刷新tiktok的token
 */
@Component
@Configuration //读取配置
@EnableScheduling // 开启定时任务
public class TiktokTokenRefreshTask {

    // 日志
    private static final Logger logger = LoggerFactory.getLogger(TiktokTokenRefreshTask.class);
    private static final int MAX_RETRY = 3;
    private static final int RETRY_INTERVAL_MS = 60_000; // 1分钟

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private TikTokTokenRefreshService tikTokTokenRefreshService;


    @Scheduled(fixedDelay = 1000 * 60 * 10L) // 每10分钟检查一次
    public void refreshTiktokToken() {
        logger.info("---TiktokTokenRefreshTask task------produce Data with fixed rate task: Execution Time - {}", DateUtil.nowDateTime());
        try {
            Integer nowTimeStamp = DateUtil.getNowTime();
            Integer accessExpireAt = Integer.valueOf(systemConfigService.getValueByKey("tiktok_access_token_expire_in")); // 绝对时间戳
            // 提前5分钟刷新
            if (accessExpireAt - nowTimeStamp < 300) {
                boolean success = false;
                Exception lastException = null;
                for (int attempt = 1; attempt <= MAX_RETRY; attempt++) {
                    try {
                        success = tikTokTokenRefreshService.refreshAndUpdateToken();
                        if (success) break;
                    } catch (Exception e) {
                        lastException = e;
                        logger.error("TiktokTokenRefreshTask: 刷新第{}次失败: {}", attempt, e.getMessage(), e);
                    }
                    if (attempt < MAX_RETRY) {
                        try {
                            Thread.sleep(RETRY_INTERVAL_MS);
                        } catch (InterruptedException ignored) {
                        }
                    }
                }
                if (!success) {
                    String alarmMsg = "Tiktok access_token 刷新连续失败" + (lastException != null ? (": " + lastException.getMessage()) : "");
                    sendAlarm(alarmMsg);
                }
            } else {
                logger.info("Tiktok access_token 未到刷新时间，当前时间: {}，过期时间: {}", nowTimeStamp, accessExpireAt);
            }
        } catch (Exception e) {
            logger.error("TiktokTokenRefreshTask.task | msg : {}", e.getMessage(), e);
            sendAlarm("TiktokTokenRefreshTask.task 执行异常: " + e.getMessage());
        }
    }

    /**
     * 失败告警方法（可对接邮件、钉钉、短信等）
     */
    protected void sendAlarm(String message) {
        // TODO: 实现告警逻辑，如邮件、钉钉、短信等
        logger.error("【TiktokTokenRefreshTask告警】{}", message);
    }
}
