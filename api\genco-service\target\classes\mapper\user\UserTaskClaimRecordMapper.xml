<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.genco.service.dao.UserTaskClaimRecordDao">

    <!-- 获取用户指定任务类型的累计兑换组数 -->
    <select id="getTotalClaimedGroups" resultType="java.lang.Integer" parameterType="map">
        SELECT COALESCE(SUM(claim_groups), 0)
        FROM eb_user_task_claim_record
        WHERE uid = #{uid} AND task_type = #{taskType} AND status = 1
    </select>

    <!-- 获取用户指定任务类型已消耗的邀请人数 -->
    <select id="getTotalUsedReferralCount" resultType="java.lang.Integer" parameterType="map">
        SELECT COALESCE(SUM(referral_count_used), 0)
        FROM eb_user_task_claim_record
        WHERE uid = #{uid} AND task_type = #{taskType} AND status = 1
    </select>

    <!-- 获取用户指定任务类型已消耗的首单人数 -->
    <select id="getTotalUsedFirstOrderCount" resultType="java.lang.Integer" parameterType="map">
        SELECT COALESCE(SUM(first_order_count_used), 0)
        FROM eb_user_task_claim_record
        WHERE uid = #{uid} AND task_type = #{taskType} AND status = 1
    </select>

</mapper>
